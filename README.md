# protobuf-deserializer

The protobuf deserializer service is responsible for:
1. reading protobuf data from the following inbound mqtt topics:
    - dt/+/stream/raw
    - dt/+/stream/eng
    - dt/+/stream/diag
    - vehicle/+/va/data/stream/raw
    - vehicle/+/va/data/stream/diag
    - vehicle/+/va/data/stream/pre
    - vehicle/+/em/data/stream/diag
    - vehicle/+/em/data/stream/pre

2. Transforming it into JSON
3. And finally publishing this enriched JSON to the following outbound Kafka topics:
    - LIVE.data-product-factory.input

Service is fully Reactive and paralleled (implemented with the [Reactor Core Framework](https://projectreactor.io/docs/core/release/reference/#_from_imperative_to_reactive_programming), and the hive-mqtt-client's [Reactor Core API](https://javadoc.io/doc/com.hivemq/hivemq-mqtt-client-reactor/latest/index.html))  
The below Diagram shows the flow of data for a single inbound and it's corresponding outbound topic. In reality this is happening thrice simultaneously, once for each inbound topic. The diagram also ignores the parallelization of the streams for the sake of simplicity.

![alt text](protobuf-deserializer.jpg "Title Text") #update diagram

## Getting started

### Generating Java sources using 'protoc' command
To generate java classes used to deserialize the protobuf messages (e.g. if there is a schema change) a compatible version of 'protoc' needs to be installed
on the users local machine.

https://protobuf.dev/installation/

Current Protoc / Java compatibility is :

protoc - 29.3
com.google.protobuf java libs - 4.29.3

There is a "compile.sh" script within ./resources/protobuf - which will compile version RawMessageType-3.2.proto of the protofuf schema

Running this will generate the src/main class files in the java_package defined in the proto file

e.g. "com.jlr.vlc.protobuf.deserializer.protocol.raw.v3"

### Generating Java test sources using 'protoc' command 
The class files in src/test/raw/v3_0 have been generated in a similar way . They are referenced in the integration test to ensure 
backwards compatibility.

Generation of these follows the same process as above

### Proto file versioning 
Historically the proto files have been kept in the resources directory with any schema changes being done in a new (major or minor) version
of the proto file.



### Running locally
To start the service locally, have an instance of [HiveMQ](https://www.hivemq.com/downloads/docker/#:~:text=%24%20docker%20run%20%2Dp%208080%3A8080%20%2Dp%201883%3A1883%20hivemq/hivemq4) and [kafka](https://kafka.apache.org/quickstart) running locally in your machine. Set environment variable as `SPRING_PROFILES_ACTIVE=local` to have the spring profile as local. 
Note, This will not require TLS configuration and uses `src/resource/application-local.yml` for startup. Then run the following command from the project folder.
```
./mvnw spring-boot:run
```

For starting the service by connecting to remote instance of HiveMQ/Kafka, configure the application accordingly for TLS connection as applicable to the environment.

## Configuring the service
Service configuration is done in the `src/resource/application.yml` file.  [HiveMQ](https://www.hivemq.com/blog/mqtt-client-library-enyclopedia-hivemq-mqtt-client).

## Testing the service locally

Configure [simulation application](https://git-gdd.sdo.jlrmotor.com/D9/simulation-platform/simulation-platform) in local as follows:

Run a simulation trip to send protobuf messages to local HiveMQ and validate the output JSON messages in local kafka topic.

## Note(s) for developers

+ Password & Username fields are each set to the value of OS environment variable `HIVEMQ_PASSWORD`, for Vault integration when deployed. For local deployment with remote HiveMQ/Kafka connectivity add a plain text file `local.env.properties` to `src/main/resources` with the following contents:
    ```
    HIVE_ESE_ENABLED=true
    HIVEMQ_PASSWORD=//whateverTheCurrentPasswordIs
    KAFKA_SASL_USERNAME=//whateverTheCurrentUsernameIs
    KAFKA_SASL_PASSWORD=//whateverTheCurrentKafkaPasswordIs
    ```
+ When running an instance of this service on your own machine for development purposes with remote HiveMQ/Kafka connectivity, please go into the `application.yml` file and edit
    ```
    spring:
      application:
        name: protobuf-deserializer
    ```  
  by appending a hypen `-` and your forename to the `name` string.  
  For example:
    ```
    spring:
      application:
        name: protobuf-deserializer-name
    ```
  This is in order to prevent conflicts between clients with identical group names, and also solves the potential issue of shared subscription behaviour interfering with developer testing/debugging.


+ Logging level is set to **INFO** in the application.yml . If you need to see logs describing:
    - Incoming message payload and topic(which includes vin/uniqueId)
    - Payload post Data Model compliant enrichment
    - What Outbound topic this converted JSON is published to (as well as other mqtt publish metadata)

  Then change the logging level to **DEBUG** by editing in the following, in the `application.yml` file.
    ```
    logging:  
      level:  
        com.jlr.vlc.protobuf.deserializer: DEBUG
    ```  
  A correlationID is logged for every message by default. It is possible to log a correlationID every n messages via configuration for each environment:
    Enabling/disabling throttle:
  ```
  logging:
    throttle:
      on: true
  ```
    Log correlationID every number of messages indicated in frequency. 
  ```
  logging:
    throttle:
      frequency: 2
  ```
  When throttle is enabled, a header (`LOG_CORRELATION_ID`) is sent downstream so that other applications will be able to log only selected correlationIDs.
  
