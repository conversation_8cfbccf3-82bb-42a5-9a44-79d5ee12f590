spring:  # Need these to prevent aws spring autoconfiguration kicking in
  autoconfigure:
    exclude:
      - io.awspring.cloud.autoconfigure.core.AwsAutoConfiguration
      - io.awspring.cloud.autoconfigure.core.CredentialsProviderAutoConfiguration
      - io.awspring.cloud.autoconfigure.config.secretsmanager.SecretsManagerAutoConfiguration

mqtt:
  automaticReconnect: false
  cleanSession: true
  retain: false
  connectionTimeout: 10
  keepAliveInterval: 120
  clientId: protobuf-deserializer-local
  protocol: non-ssl
  hostname: localhost
  port: 1883
  qosLevel: 1
  groupId: ${spring.application.name}
  topics:
    testTopic: test/topic

hivemq:
  eseEnabled: false

statsd:
  hostname: localhost
  port: 8125

kafka:
  bootStrapServers: localhost:9092
  clientId: ${spring.application.name}
  retryBackoff: 10000
  maxInFlightRequests: 10
  acksConfig: all
  securityProtocol: PLAINTEXT
  outboundTopic: test-topic

ssl:
  password: password
  mqttClientCa: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUNVVENDQWZ1Z0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRUUZBREJYTVFzd0NRWURWUVFHRXdKRFRqRUwKTUFrR0ExVUVDQk1DVUU0eEN6QUpCZ05WQkFjVEFrTk9NUXN3Q1FZRFZRUUtFd0pQVGpFTE1Ba0dBMVVFQ3hNQwpWVTR4RkRBU0JnTlZCQU1UQzBobGNtOXVaeUJaWVc1bk1CNFhEVEExTURjeE5USXhNVGswTjFvWERUQTFNRGd4Ck5ESXhNVGswTjFvd1Z6RUxNQWtHQTFVRUJoTUNRMDR4Q3pBSkJnTlZCQWdUQWxCT01Rc3dDUVlEVlFRSEV3SkQKVGpFTE1Ba0dBMVVFQ2hNQ1QwNHhDekFKQmdOVkJBc1RBbFZPTVJRd0VnWURWUVFERXd0SVpYSnZibWNnV1dGdQpaekJjTUEwR0NTcUdTSWIzRFFFQkFRVUFBMHNBTUVnQ1FRQ3A1aG5HN29nQmh0bHlucE9TMjFjQmV3S0UvQjdqClYxNHFleXNsbnIyNnhaVXNTVmtvMzZabmhpYU8vemJNT29SY0tLOXZFY2dNdGNMRnVRVFdEbDNSQWdNQkFBR2oKZ2JFd2dhNHdIUVlEVlIwT0JCWUVGRlhJNzBrclhlUUR4WmdiYUNRb1I0alVEbmNFTUg4R0ExVWRJd1I0TUhhQQpGRlhJNzBrclhlUUR4WmdiYUNRb1I0alVEbmNFb1Z1a1dUQlhNUXN3Q1FZRFZRUUdFd0pEVGpFTE1Ba0dBMVVFCkNCTUNVRTR4Q3pBSkJnTlZCQWNUQWtOT01Rc3dDUVlEVlFRS0V3SlBUakVMTUFrR0ExVUVDeE1DVlU0eEZEQVMKQmdOVkJBTVRDMGhsY205dVp5QlpZVzVuZ2dFQU1Bd0dBMVVkRXdRRk1BTUJBZjh3RFFZSktvWklodmNOQVFFRQpCUUFEUVFBL3VnekJyampLOWpjV25EVmZHSGxrM2ljTlJxMG9WN1JpMzJ6LytIUVg2N2FSZmdadTdLV2RJK0p1CldtN0RDZnJQTkdWd0ZXVVFPbXNQdWU5clpCZ08KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQ==
  mqttClientCert: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUNVVENDQWZ1Z0F3SUJBZ0lCQURBTkJna3Foa2lHOXcwQkFRUUZBREJYTVFzd0NRWURWUVFHRXdKRFRqRUwKTUFrR0ExVUVDQk1DVUU0eEN6QUpCZ05WQkFjVEFrTk9NUXN3Q1FZRFZRUUtFd0pQVGpFTE1Ba0dBMVVFQ3hNQwpWVTR4RkRBU0JnTlZCQU1UQzBobGNtOXVaeUJaWVc1bk1CNFhEVEExTURjeE5USXhNVGswTjFvWERUQTFNRGd4Ck5ESXhNVGswTjFvd1Z6RUxNQWtHQTFVRUJoTUNRMDR4Q3pBSkJnTlZCQWdUQWxCT01Rc3dDUVlEVlFRSEV3SkQKVGpFTE1Ba0dBMVVFQ2hNQ1QwNHhDekFKQmdOVkJBc1RBbFZPTVJRd0VnWURWUVFERXd0SVpYSnZibWNnV1dGdQpaekJjTUEwR0NTcUdTSWIzRFFFQkFRVUFBMHNBTUVnQ1FRQ3A1aG5HN29nQmh0bHlucE9TMjFjQmV3S0UvQjdqClYxNHFleXNsbnIyNnhaVXNTVmtvMzZabmhpYU8vemJNT29SY0tLOXZFY2dNdGNMRnVRVFdEbDNSQWdNQkFBR2oKZ2JFd2dhNHdIUVlEVlIwT0JCWUVGRlhJNzBrclhlUUR4WmdiYUNRb1I0alVEbmNFTUg4R0ExVWRJd1I0TUhhQQpGRlhJNzBrclhlUUR4WmdiYUNRb1I0alVEbmNFb1Z1a1dUQlhNUXN3Q1FZRFZRUUdFd0pEVGpFTE1Ba0dBMVVFCkNCTUNVRTR4Q3pBSkJnTlZCQWNUQWtOT01Rc3dDUVlEVlFRS0V3SlBUakVMTUFrR0ExVUVDeE1DVlU0eEZEQVMKQmdOVkJBTVRDMGhsY205dVp5QlpZVzVuZ2dFQU1Bd0dBMVVkRXdRRk1BTUJBZjh3RFFZSktvWklodmNOQVFFRQpCUUFEUVFBL3VnekJyampLOWpjV25EVmZHSGxrM2ljTlJxMG9WN1JpMzJ6LytIUVg2N2FSZmdadTdLV2RJK0p1CldtN0RDZnJQTkdWd0ZXVVFPbXNQdWU5clpCZ08KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQ==
  mqttClientKey: 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

keycloak:
  authTokenUri: http://localhost:8088/auth/openid-connect/token
  clientID: client-id
  privateKeyPassword: changeme
  privateKey: 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
  grantType: client_credentials
  assertionType: urn:ietf:params:oauth:client-assertion-type:jwt-bearer