// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: RawMessageType-3.0.proto
// Protobuf Java Version: 4.29.3

package raw.v3_0;

public final class RawProtobufMessage {
  private RawProtobufMessage() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 3,
      /* suffix= */ "",
      RawProtobufMessage.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_jlr_queue_protobuf_ResponseData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_jlr_queue_protobuf_ResponseData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_jlr_queue_protobuf_ResponseData_dataType_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_jlr_queue_protobuf_ResponseData_dataType_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_jlr_queue_protobuf_ResponseData_GlobalRT_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_jlr_queue_protobuf_ResponseData_GlobalRT_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_jlr_queue_protobuf_ResponseData_Data_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_jlr_queue_protobuf_ResponseData_Data_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_jlr_queue_protobuf_ResponseData_Data_Samples_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_jlr_queue_protobuf_ResponseData_Data_Samples_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_jlr_queue_protobuf_ResponseData_Data_CalculatedData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_jlr_queue_protobuf_ResponseData_Data_CalculatedData_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030RawMessageType-3.0.proto\022\022jlr.queue.pr" +
      "otobuf\032\036google/protobuf/wrappers.proto\"\253" +
      "\006\n\014ResponseData\022\020\n\010query_id\030\001 \001(\t\022\032\n\022eve" +
      "nt_timestamp_ms\030\002 \001(\004\0223\n\004data\030\003 \003(\0132%.jl" +
      "r.queue.protobuf.ResponseData.Data\022;\n\010gl" +
      "obalRT\030\004 \001(\0132).jlr.queue.protobuf.Respon" +
      "seData.GlobalRT\032p\n\010dataType\022\023\n\tdata_uint" +
      "\030\001 \001(\004H\000\022\024\n\ndata_float\030\002 \001(\002H\000\022\023\n\tdata_b" +
      "yte\030\003 \001(\014H\000\022\025\n\013data_string\030\004 \001(\tH\000B\r\n\013va" +
      "lue_oneof\0325\n\010GlobalRT\022\020\n\010RT_value\030\001 \001(\004\022" +
      "\027\n\017RT_timestamp_ms\030\002 \001(\004\032\321\003\n\004Data\022\017\n\007dat" +
      "a_id\030\001 \001(\t\022>\n\007samples\030\002 \003(\0132-.jlr.queue." +
      "protobuf.ResponseData.Data.Samples\022B\n\004ca" +
      "lc\030\003 \001(\01324.jlr.queue.protobuf.ResponseDa" +
      "ta.Data.CalculatedData\032Z\n\007Samples\022\024\n\014tim" +
      "estamp_ms\030\001 \001(\004\0229\n\006sample\030\002 \001(\0132).jlr.qu" +
      "eue.protobuf.ResponseData.dataType\032\327\001\n\016C" +
      "alculatedData\022\023\n\013end_time_ms\030\001 \001(\004\022\023\n\013du" +
      "ration_ms\030\002 \001(\r\022\013\n\003avg\030\003 \001(\002\0226\n\003min\030\004 \001(" +
      "\0132).jlr.queue.protobuf.ResponseData.data" +
      "Type\0226\n\003max\030\005 \001(\0132).jlr.queue.protobuf.R" +
      "esponseData.dataType\022\017\n\007std_dev\030\006 \001(\002\022\r\n" +
      "\005count\030\007 \001(\rB \n\010raw.v3_0B\022RawProtobufMes" +
      "sageP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.WrappersProto.getDescriptor(),
        });
    internal_static_jlr_queue_protobuf_ResponseData_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_jlr_queue_protobuf_ResponseData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_jlr_queue_protobuf_ResponseData_descriptor,
        new java.lang.String[] { "QueryId", "EventTimestampMs", "Data", "GlobalRT", });
    internal_static_jlr_queue_protobuf_ResponseData_dataType_descriptor =
      internal_static_jlr_queue_protobuf_ResponseData_descriptor.getNestedTypes().get(0);
    internal_static_jlr_queue_protobuf_ResponseData_dataType_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_jlr_queue_protobuf_ResponseData_dataType_descriptor,
        new java.lang.String[] { "DataUint", "DataFloat", "DataByte", "DataString", "ValueOneof", });
    internal_static_jlr_queue_protobuf_ResponseData_GlobalRT_descriptor =
      internal_static_jlr_queue_protobuf_ResponseData_descriptor.getNestedTypes().get(1);
    internal_static_jlr_queue_protobuf_ResponseData_GlobalRT_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_jlr_queue_protobuf_ResponseData_GlobalRT_descriptor,
        new java.lang.String[] { "RTValue", "RTTimestampMs", });
    internal_static_jlr_queue_protobuf_ResponseData_Data_descriptor =
      internal_static_jlr_queue_protobuf_ResponseData_descriptor.getNestedTypes().get(2);
    internal_static_jlr_queue_protobuf_ResponseData_Data_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_jlr_queue_protobuf_ResponseData_Data_descriptor,
        new java.lang.String[] { "DataId", "Samples", "Calc", });
    internal_static_jlr_queue_protobuf_ResponseData_Data_Samples_descriptor =
      internal_static_jlr_queue_protobuf_ResponseData_Data_descriptor.getNestedTypes().get(0);
    internal_static_jlr_queue_protobuf_ResponseData_Data_Samples_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_jlr_queue_protobuf_ResponseData_Data_Samples_descriptor,
        new java.lang.String[] { "TimestampMs", "Sample", });
    internal_static_jlr_queue_protobuf_ResponseData_Data_CalculatedData_descriptor =
      internal_static_jlr_queue_protobuf_ResponseData_Data_descriptor.getNestedTypes().get(1);
    internal_static_jlr_queue_protobuf_ResponseData_Data_CalculatedData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_jlr_queue_protobuf_ResponseData_Data_CalculatedData_descriptor,
        new java.lang.String[] { "EndTimeMs", "DurationMs", "Avg", "Min", "Max", "StdDev", "Count", });
    descriptor.resolveAllFeaturesImmutable();
    com.google.protobuf.WrappersProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
