// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: RawMessageType-3.0.proto
// Protobuf Java Version: 4.29.3

package raw.v3_0;

public interface ResponseDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:jlr.queue.protobuf.ResponseData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string query_id = 1;</code>
   * @return The queryId.
   */
  java.lang.String getQueryId();
  /**
   * <code>string query_id = 1;</code>
   * @return The bytes for queryId.
   */
  com.google.protobuf.ByteString
      getQueryIdBytes();

  /**
   * <code>uint64 event_timestamp_ms = 2;</code>
   * @return The eventTimestampMs.
   */
  long getEventTimestampMs();

  /**
   * <code>repeated .jlr.queue.protobuf.ResponseData.Data data = 3;</code>
   */
  java.util.List<raw.v3_0.ResponseData.Data> 
      getDataList();
  /**
   * <code>repeated .jlr.queue.protobuf.ResponseData.Data data = 3;</code>
   */
  raw.v3_0.ResponseData.Data getData(int index);
  /**
   * <code>repeated .jlr.queue.protobuf.ResponseData.Data data = 3;</code>
   */
  int getDataCount();
  /**
   * <code>repeated .jlr.queue.protobuf.ResponseData.Data data = 3;</code>
   */
  java.util.List<? extends raw.v3_0.ResponseData.DataOrBuilder> 
      getDataOrBuilderList();
  /**
   * <code>repeated .jlr.queue.protobuf.ResponseData.Data data = 3;</code>
   */
  raw.v3_0.ResponseData.DataOrBuilder getDataOrBuilder(
      int index);

  /**
   * <code>.jlr.queue.protobuf.ResponseData.GlobalRT globalRT = 4;</code>
   * @return Whether the globalRT field is set.
   */
  boolean hasGlobalRT();
  /**
   * <code>.jlr.queue.protobuf.ResponseData.GlobalRT globalRT = 4;</code>
   * @return The globalRT.
   */
  raw.v3_0.ResponseData.GlobalRT getGlobalRT();
  /**
   * <code>.jlr.queue.protobuf.ResponseData.GlobalRT globalRT = 4;</code>
   */
  raw.v3_0.ResponseData.GlobalRTOrBuilder getGlobalRTOrBuilder();
}
