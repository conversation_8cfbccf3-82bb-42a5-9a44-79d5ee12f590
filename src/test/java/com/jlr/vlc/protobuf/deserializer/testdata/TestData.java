/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.testdata;

import com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData;

public class TestData {

  public static final String DATA_ID_1 = "data-id-1";
  public static final String DATA_ID_2 = "data-id-2";
  public static final ResponseData.Data.CalculatedData CALCULATED_DATA =
      ResponseData.Data.CalculatedData.newBuilder()
          .setMax(ResponseData.dataType.newBuilder().setDataUint(20).build())
          .setMin(ResponseData.dataType.newBuilder().setDataFloat(10f).build())
          .setEndTimeMs(1000L)
          .setDurationMs(2000)
          .setAvg(23.76f)
          .setStdDev(0.67f)
          .setCount(1)
          .build();

  public static final raw.v3_0.ResponseData.Data.CalculatedData CALCULATED_DATA_V3_0 =
      raw.v3_0.ResponseData.Data.CalculatedData.newBuilder()
          .setMax(raw.v3_0.ResponseData.dataType.newBuilder().setDataUint(20).build())
          .setMin(raw.v3_0.ResponseData.dataType.newBuilder().setDataFloat(10f).build())
          .setEndTimeMs(1000L)
          .setDurationMs(2000)
          .setAvg(23.76f)
          .setStdDev(0.67f)
          .setCount(1)
          .build();


  public static final ResponseData.Data.Samples SAMPLES = ResponseData.Data.Samples.newBuilder()
      .setTimestampMs(100L)
      .setSample(ResponseData.dataType.newBuilder().setDataString("String").build())
      .build();

  public static final raw.v3_0.ResponseData.Data.Samples SAMPLES_V3_0 =
      raw.v3_0.ResponseData.Data.Samples.newBuilder()
          .setTimestampMs(100L)
          .setSample(raw.v3_0.ResponseData.dataType.newBuilder().setDataUint(200L).build())
          .build();

  public static final ResponseData.Data DATA = ResponseData.Data.newBuilder()
      .setDataId(DATA_ID_2)
      .addSamples(SAMPLES)
      .setCalc(CALCULATED_DATA)
      .build();

  public static final raw.v3_0.ResponseData.Data DATA_V3_0 = raw.v3_0.ResponseData.Data.newBuilder()
      .setDataId(DATA_ID_1)
      .addSamples(SAMPLES_V3_0)
      .setCalc(CALCULATED_DATA_V3_0)
      .build();

  public static final ResponseData.GlobalRT GLOBAL_RT = ResponseData.GlobalRT.newBuilder()
      .setRTTimestampMs(32325422L).setRTValue(23L)
      .build();

  public static final raw.v3_0.ResponseData.GlobalRT GLOBAL_RT_V3_0 =
      raw.v3_0.ResponseData.GlobalRT.newBuilder()
          .setRTTimestampMs(32325422L).setRTValue(23L)
          .build();

  public static final String SESSION_POWERCYCLE = "Powercycle";
  public static final ResponseData RESPONSE_DATA = ResponseData.newBuilder()
      .addData(DATA)
      .setGlobalRT(GLOBAL_RT)
      .setEmitTimestampMs(0)
      .setSessionPowercycle(SESSION_POWERCYCLE)
      .build();

  public static final raw.v3_0.ResponseData RESPONSE_DATA_V3 = raw.v3_0.ResponseData.newBuilder()
      .addData(DATA_V3_0)
      .setGlobalRT(GLOBAL_RT_V3_0)
      .build();

}
