/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.client;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.hivemq.client.mqtt.MqttClientSslConfig;
import com.hivemq.client.mqtt.MqttClientTransportConfig;
import com.hivemq.client.mqtt.datatypes.MqttClientIdentifier;
import com.hivemq.client.mqtt.datatypes.MqttUtf8String;
import com.hivemq.client.mqtt.mqtt5.Mqtt5Client;
import com.hivemq.client.mqtt.mqtt5.Mqtt5ClientConfig;
import com.hivemq.client.mqtt.mqtt5.reactor.Mqtt5ReactorClient;
import com.jlr.vlc.protobuf.deserializer.config.FeatureToggleConfig;
import com.jlr.vlc.protobuf.deserializer.config.MqttConfig;
import com.jlr.vlc.protobuf.deserializer.config.SecretsHandlerService;
import com.jlr.vlc.protobuf.deserializer.config.SslConfig;
import com.jlr.vlc.protobuf.deserializer.keycloakapiclient.KeycloakApiClientServiceImpl;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.TrustManagerFactory;
import org.apache.commons.io.IOUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class HiveMqClientBuilderTest {

  private static final String BASE_64_MQTT_CERT = loadResource("base64MqttCert.txt");
  private static final String BASE_64_MQTT_KEY = loadResource("base64MqttKey.txt");

  private static final String MQTT_CERT = decodeBase64String(BASE_64_MQTT_CERT);
  private static final String MQTT_KEY = decodeBase64String(BASE_64_MQTT_KEY);

  @Mock
  private SecretsHandlerService secretsHandlerService;

  @Mock
  private FeatureToggleConfig featureToggleConfig;

  @Test
  void testGetSslConfig() {
    KeyManagerFactory keyManagerFactory = mock(KeyManagerFactory.class);
    TrustManagerFactory trustManagerFactory = mock(TrustManagerFactory.class);
    MqttClientSslConfig sslConfig =
        HiveMqClientBuilder.getSslConfig(keyManagerFactory, trustManagerFactory);
    assertThat(sslConfig.getKeyManagerFactory()).contains(keyManagerFactory);
    assertThat(sslConfig.getTrustManagerFactory()).contains(trustManagerFactory);
  }

  @Test
  void testGetTransportConfig() {
    MqttConfig mqttConfig = mock(MqttConfig.class);
    KeyManagerFactory keyManagerFactory = mock(KeyManagerFactory.class);
    TrustManagerFactory trustManagerFactory = mock(TrustManagerFactory.class);
    MqttClientSslConfig sslConfig =
        HiveMqClientBuilder.getSslConfig(keyManagerFactory, trustManagerFactory);
    when(mqttConfig.getHostname()).thenReturn("example.org");
    when(mqttConfig.getPort()).thenReturn(8000);
    when(mqttConfig.getConnectionTimeout()).thenReturn(10);

    MqttClientTransportConfig transportConfig =
        HiveMqClientBuilder.getTransportConfig(mqttConfig, sslConfig);
    assertThat(transportConfig.getSslConfig()).contains(sslConfig);
    assertThat(transportConfig.getServerAddress().getHostName()).isEqualTo(
        mqttConfig.getHostname());
    assertThat(transportConfig.getServerAddress().getPort()).isEqualTo(mqttConfig.getPort());
    assertThat(transportConfig.getSocketConnectTimeoutMs()).isEqualTo(
        mqttConfig.getConnectionTimeout() * 1000);
  }

  void testGetClient(boolean withAutomaticReconnect) {
    final KeycloakApiClientServiceImpl keycloakApiClient = mock(KeycloakApiClientServiceImpl.class);
    MqttConfig mqttConfig = mock(MqttConfig.class);
    final KeyManagerFactory keyManagerFactory = mock(KeyManagerFactory.class);
    final TrustManagerFactory trustManagerFactory = mock(TrustManagerFactory.class);

    when(mqttConfig.getHostname()).thenReturn("example.org");
    when(mqttConfig.getPort()).thenReturn(8000);
    when(mqttConfig.getConnectionTimeout()).thenReturn(10);

    if (withAutomaticReconnect) {
      when(mqttConfig.isAutomaticReconnect()).thenReturn(true);
    } else {
      when(mqttConfig.isAutomaticReconnect()).thenReturn(false);
    }

    MqttClientSslConfig sslConfig =
        HiveMqClientBuilder.getSslConfig(keyManagerFactory, trustManagerFactory);
    MqttClientTransportConfig transportConfig =
        HiveMqClientBuilder.getTransportConfig(mqttConfig, sslConfig);

    when(mqttConfig.getClientId()).thenReturn("clientId");
    HiveMqClientBuilder hiveMqClientBuilder =
        new HiveMqClientBuilder(keycloakApiClient, secretsHandlerService, featureToggleConfig);
    String token = "token";
    Mqtt5Client mqtt5Client = hiveMqClientBuilder.buildClient(token, transportConfig, mqttConfig);
    assertThat(mqtt5Client).isNotNull();
    Mqtt5ClientConfig config = mqtt5Client.getConfig();
    assertThat(config.getClientIdentifier()).contains(
        MqttClientIdentifier.of(mqttConfig.getClientId()));
    assertThat(config.getTransportConfig()).isEqualTo(transportConfig);
    assertThat(config.getSimpleAuth()).isPresent();
    assertThat(config.getSimpleAuth().get().getUsername()).contains(
        MqttUtf8String.of(mqttConfig.getClientId()));
    assertThat(config.getSimpleAuth().get().getPassword()).contains(
        ByteBuffer.wrap(token.getBytes()));

    if (withAutomaticReconnect) {
      assertThat(config.getDisconnectedListeners()).isNotEmpty();
    } else {
      assertThat(config.getDisconnectedListeners()).isEmpty();
    }
  }

  @Test
  void testGetClientWithoutAutomaticReconnect() {
    testGetClient(false);
  }

  @Test
  void testGetClientWithAutomaticReconnect() {
    testGetClient(true);
  }

  @Test
  void testGetMqttClient() {
    KeycloakApiClientServiceImpl keycloakApiClient = mock(KeycloakApiClientServiceImpl.class);
    when(keycloakApiClient.getTokenForClient()).thenReturn(
        Mono.just("{ \"access_token\": \"token\" }"));
    HiveMqClientBuilder hiveMqClientBuilder =
        new HiveMqClientBuilder(keycloakApiClient, secretsHandlerService, featureToggleConfig);
    MqttConfig mqttConfig = mock(MqttConfig.class);
    when(mqttConfig.getHostname()).thenReturn("example.org");
    when(mqttConfig.getPort()).thenReturn(8000);
    when(mqttConfig.getConnectionTimeout()).thenReturn(10);
    when(mqttConfig.getClientId()).thenReturn("clientId");

    SslConfig sslConfig = mock(SslConfig.class);
    when(sslConfig.getMqttClientCa()).thenReturn(BASE_64_MQTT_CERT);
    when(sslConfig.getMqttClientCert()).thenReturn(BASE_64_MQTT_CERT);
    when(sslConfig.getMqttClientKey()).thenReturn(BASE_64_MQTT_KEY);
    when(sslConfig.getPassword()).thenReturn("password");

    Mqtt5ReactorClient mqtt5ReactorClient = hiveMqClientBuilder.mqtt5Client(mqttConfig, sslConfig);

    assertThat(mqtt5ReactorClient).isNotNull();
  }

  @Test
  void testGetMqttClient_whenIsUpdatedHiveMqCertTrue() {
    when(secretsHandlerService.getMqttClientCertSecretValue()).thenReturn(MQTT_CERT);
    when(secretsHandlerService.getMqttClientKeySecretValue()).thenReturn(MQTT_KEY);
    when(secretsHandlerService.getMqttClientPassphraseSecretValue()).thenReturn("password");

    KeycloakApiClientServiceImpl keycloakApiClient = mock(KeycloakApiClientServiceImpl.class);
    when(keycloakApiClient.getTokenForClient()).thenReturn(
        Mono.just("{ \"access_token\": \"token\" }"));
    HiveMqClientBuilder hiveMqClientBuilder =
        new HiveMqClientBuilder(keycloakApiClient, secretsHandlerService, featureToggleConfig);
    MqttConfig mqttConfig = mock(MqttConfig.class);
    when(mqttConfig.getHostname()).thenReturn("example.org");
    when(mqttConfig.getPort()).thenReturn(8000);
    when(mqttConfig.getConnectionTimeout()).thenReturn(10);
    when(mqttConfig.getClientId()).thenReturn("clientId");

    when(featureToggleConfig.isUpdatedHiveMqCert()).thenReturn(true);

    SslConfig sslConfig = mock(SslConfig.class);
    when(sslConfig.getMqttClientCa()).thenReturn(BASE_64_MQTT_CERT);

    Mqtt5ReactorClient mqtt5ReactorClient = hiveMqClientBuilder.mqtt5Client(mqttConfig, sslConfig);

    assertThat(mqtt5ReactorClient).isNotNull();
  }

  static String decodeBase64String(String sslCert) {
    return new String(Base64.getDecoder().decode(sslCert));
  }

  private static String loadResource(String resource) {
    try {
      return IOUtils.resourceToString(resource, StandardCharsets.UTF_8,
          ClassLoader.getSystemClassLoader());
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }
}
