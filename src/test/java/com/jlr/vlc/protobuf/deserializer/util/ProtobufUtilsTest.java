/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.util;

import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.ByteString;
import com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData;
import org.junit.jupiter.api.Test;

public class ProtobufUtilsTest {

  @Test
  void shouldSetDataUint() {
    var dataType = ResponseData.dataType.newBuilder().setDataUint(1L).build();
    Object result = ProtobufUtils.getOneOfValue(dataType);
    assertThat(result).isEqualTo(1L);
  }

  @Test
  void shouldSetDataFloat() {
    var dataType = ResponseData.dataType.newBuilder().setDataFloat(2.34f).build();
    Object result = ProtobufUtils.getOneOfValue(dataType);
    assertThat(result).isEqualTo(2.34f);
  }

  @Test
  void shouldSetDataByte() {
    // This is set to convert bytes to hex
    var bytes = new String("some bytes").getBytes();
    var hex = "736F6D65206279746573";
    var byteString = ByteString.copyFrom(bytes);
    var dataType = ResponseData.dataType.newBuilder().setDataByte(byteString).build();
    Object result = ProtobufUtils.getOneOfValue(dataType);
    assertThat(result).isEqualTo(hex);
  }

  @Test
  void shouldSetDataString() {
    var dataType = ResponseData.dataType.newBuilder().setDataString("some string").build();
    Object result = ProtobufUtils.getOneOfValue(dataType);
    assertThat(result).isEqualTo("some string");
  }

  @Test
  void shouldSetDataUint32() {
    var dataType = ResponseData.dataType.newBuilder().setDataUint32(32).build();
    Object result = ProtobufUtils.getOneOfValue(dataType);
    assertThat(result).isEqualTo(32);
  }

  @Test
  void shouldSetDataBool() {
    var dataType = ResponseData.dataType.newBuilder().setDataBool(true).build();
    Object result = ProtobufUtils.getOneOfValue(dataType);
    assertThat(result).isEqualTo(true);
  }

  @Test
  void shouldSetDataInt32() {
    var dataType = ResponseData.dataType.newBuilder().setDataInt32(32).build();
    Object result = ProtobufUtils.getOneOfValue(dataType);
    assertThat(result).isEqualTo(32);
  }

  @Test
  void shouldSetDataInt64() {
    var dataType = ResponseData.dataType.newBuilder().setDataInt64(64).build();
    Object result = ProtobufUtils.getOneOfValue(dataType);
    assertThat(result).isEqualTo(64L);
  }

  @Test
  void shouldSetDataDouble() {
    var dataType = ResponseData.dataType.newBuilder().setDataDouble(25.6666d).build();
    Object result = ProtobufUtils.getOneOfValue(dataType);
    assertThat(result).isEqualTo(25.6666d);
  }

}
