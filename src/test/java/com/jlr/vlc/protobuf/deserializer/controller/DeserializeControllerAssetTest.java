/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.controller;

import static org.assertj.core.api.Assertions.assertThat;

import com.hivemq.client.mqtt.datatypes.MqttTopic;
import com.hivemq.client.mqtt.mqtt5.datatypes.Mqtt5UserProperties;
import com.hivemq.client.mqtt.mqtt5.datatypes.Mqtt5UserProperty;
import com.hivemq.client.mqtt.mqtt5.message.publish.Mqtt5Publish;
import com.jlr.vlc.protobuf.deserializer.testdata.TestData;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DeserializeControllerAssetTest {

  @InjectMocks
  private DeserializeControllerAsset deserializeControllerAsset;
  @Mock
  private Mqtt5Publish message;

  private static final String EVA2_ID = "b6b07467-9099-4e84-8d09-38161c70dcb9";
  private static final String EVA25_ID = "b6b07467-9099-4e84-8d09-38161c70dcb9_vcm";

  private static MqttTopic mqttTopicEva2 = MqttTopic.builder()
      .addLevel("dt").addLevel(EVA2_ID).addLevel("stream").addLevel("raw")
      .build();

  private static MqttTopic mqttTopicEva25 = MqttTopic.builder()
      .addLevel("vehicle").addLevel(EVA25_ID).addLevel("em").addLevel("data").addLevel("stream")
      .addLevel("pre")
      .build();

  @Test
  void shouldMapFieldsToKafkaResponse_forEva2Vehicle() {

    Mockito.when(message.getTopic()).thenReturn(mqttTopicEva2);
    var responseData = TestData.RESPONSE_DATA_V3.toBuilder()
        .setEventTimestampMs(1000)
        .setQueryId("query-id")
        .build();
    Mockito.when(message.getPayloadAsBytes()).thenReturn(responseData.toByteArray());
    var userProperty = Mqtt5UserProperty.of("fleetId", "FLEET1");
    Mockito.when(message.getUserProperties()).thenReturn(Mqtt5UserProperties.of(userProperty));
    JSONObject kafkaResponse = deserializeControllerAsset.apply(message);

    assertThat(kafkaResponse.getString("unique_id")).isEqualTo(EVA2_ID);
    assertThat(kafkaResponse.getString("query_id")).isEqualTo("query-id");
    assertThat(kafkaResponse.getLong("event_timestamp_ms")).isEqualTo(1000);
    assertThat(kafkaResponse.getLong("emit_timestamp_ms")).isEqualTo(0);
    assertThat(kafkaResponse.getString("session_powercycle")).isEqualTo("");
    assertThat(kafkaResponse.getString("fleet_id")).isEqualTo("FLEET1");

    JSONObject calc = kafkaResponse.getJSONArray("data").getJSONObject(0).getJSONObject("calc");

    assertThat(calc.getLong("end_time_ms")).isEqualTo(1000L);
    assertThat(calc.getLong("duration_ms")).isEqualTo(2000);
    assertThat(calc.getLong("count")).isEqualTo(1);
    assertThat(calc.getFloat("avg")).isEqualTo(23.76f);
    assertThat(calc.getFloat("std_dev")).isEqualTo(0.67f);
    assertThat(calc.getJSONObject("max").getInt("value")).isEqualTo(20);
    assertThat(calc.getJSONObject("min").getInt("value")).isEqualTo(10);

    JSONObject sample = kafkaResponse.getJSONArray("data").getJSONObject(0).getJSONArray("samples")
        .getJSONObject(0);

    assertThat(sample.getString("type")).isEqualTo("DATA_UINT");
    assertThat(sample.getInt("timestamp_ms")).isEqualTo(100);
    assertThat(sample.getInt("value")).isEqualTo(200);

    JSONObject globalRT = kafkaResponse.getJSONObject("globalRT");

    assertThat(globalRT.getLong("RT_value")).isEqualTo(23L);
    assertThat(globalRT.getLong("RT_timestamp_ms")).isEqualTo(32325422L);

    String dataId = kafkaResponse.getJSONArray("data").getJSONObject(0).getString("data_id");
    assertThat(dataId).isEqualTo(TestData.DATA_ID_1);

  }

  @Test
  void shouldMapFieldsToKafkaResponse_forEva25Vehicle() {

    Mockito.when(message.getTopic()).thenReturn(mqttTopicEva25);
    var responseData = TestData.RESPONSE_DATA.toBuilder()
        .setEventTimestampMs(1000)
        .setQueryId("query-id")
        .setEmitTimestampMs(500)
        .build();
    Mockito.when(message.getPayloadAsBytes()).thenReturn(responseData.toByteArray());
    var userProperty = Mqtt5UserProperty.of("fleetId", "FLEET1");
    Mockito.when(message.getUserProperties()).thenReturn(Mqtt5UserProperties.of(userProperty));

    JSONObject kafkaResponse = deserializeControllerAsset.apply(message);

    assertThat(kafkaResponse.getString("unique_id")).isEqualTo(EVA2_ID);
    assertThat(kafkaResponse.getString("query_id")).isEqualTo("query-id");
    assertThat(kafkaResponse.getLong("event_timestamp_ms")).isEqualTo(1000);
    assertThat(kafkaResponse.getLong("emit_timestamp_ms")).isEqualTo(500);
    assertThat(kafkaResponse.getString("session_powercycle")).isEqualTo("Powercycle");
    assertThat(kafkaResponse.getString("fleet_id")).isEqualTo("FLEET1");

    JSONObject calc = kafkaResponse.getJSONArray("data").getJSONObject(0).getJSONObject("calc");

    assertThat(calc.getLong("end_time_ms")).isEqualTo(1000L);
    assertThat(calc.getLong("duration_ms")).isEqualTo(2000);
    assertThat(calc.getLong("count")).isEqualTo(1);
    assertThat(calc.getFloat("avg")).isEqualTo(23.76f);
    assertThat(calc.getFloat("std_dev")).isEqualTo(0.67f);
    assertThat(calc.getJSONObject("max").getInt("value")).isEqualTo(20);
    assertThat(calc.getJSONObject("min").getInt("value")).isEqualTo(10);

    JSONObject sample = kafkaResponse.getJSONArray("data").getJSONObject(0).getJSONArray("samples")
        .getJSONObject(0);

    assertThat(sample.getString("type")).isEqualTo("DATA_STRING");
    assertThat(sample.getInt("timestamp_ms")).isEqualTo(100);
    assertThat(sample.getString("value")).isEqualTo("String");

    JSONObject globalRT = kafkaResponse.getJSONObject("globalRT");

    assertThat(globalRT.getLong("RT_value")).isEqualTo(23L);
    assertThat(globalRT.getLong("RT_timestamp_ms")).isEqualTo(32325422L);

    String dataId = kafkaResponse.getJSONArray("data").getJSONObject(0).getString("data_id");
    assertThat(dataId).isEqualTo(TestData.DATA_ID_2);

  }

}