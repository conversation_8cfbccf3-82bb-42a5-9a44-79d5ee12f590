/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.integration;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.NewTopic;
import org.springframework.boot.test.util.TestPropertyValues;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.TopicBuilder;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.hivemq.HiveMQContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

@Testcontainers
@Configuration
@Slf4j
public class IntegrationTestConfiguration {

  @Container
  public static final HiveMQContainer hiveMqContainer
      = new HiveMQContainer(DockerImageName.parse("hivemq/hivemq4:latest"));

  @Container
  public static final KafkaContainer kafkaContainer
      = new KafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.3.2"));

  public static final String outputKafkaTopic = "test-topic";

  static class TestApplicationContextInitializer
      implements ApplicationContextInitializer<ConfigurableApplicationContext> {

    @Override
    public void initialize(final ConfigurableApplicationContext applicationContext) {
      hiveMqContainer.start();
      kafkaContainer.start();
      TestPropertyValues.of(
          "mqtt.port=" + hiveMqContainer.getMqttPort(),
          "kafka.bootStrapServers=" + kafkaContainer.getBootstrapServers(),
          "spring.kafka.bootstrap-servers=" + kafkaContainer.getBootstrapServers()
      ).applyTo(applicationContext.getEnvironment());

      Map<String, Object> kafkaProperties = new HashMap<>();
      kafkaProperties.put("bootstrap.servers", kafkaContainer.getBootstrapServers());
      AdminClient adminClient = AdminClient.create(kafkaProperties);

      Collection<NewTopic> newTopics = new ArrayList<>();
      newTopics.add(TopicBuilder.name(outputKafkaTopic).partitions(1).replicas(1).build());

      adminClient.createTopics(newTopics);
    }

  }

}
