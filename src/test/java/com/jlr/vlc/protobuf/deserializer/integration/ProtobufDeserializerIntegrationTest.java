/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.integration;

import static com.jlr.vlc.protobuf.deserializer.integration.IntegrationTestConfiguration.outputKafkaTopic;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.hivemq.client.mqtt.mqtt5.Mqtt5BlockingClient;
import com.hivemq.client.mqtt.mqtt5.Mqtt5Client;
import com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData;
import com.jlr.vlc.protobuf.deserializer.testdata.TestData;
import com.timgroup.statsd.StatsDClient;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import org.apache.commons.io.IOUtils;
import org.json.JSONObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.skyscreamer.jsonassert.JSONAssert;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;

@ActiveProfiles("test")
@SpringBootTest
@ContextConfiguration(initializers = IntegrationTestConfiguration.TestApplicationContextInitializer.class)
@DirtiesContext(classMode = DirtiesContext.ClassMode.BEFORE_EACH_TEST_METHOD)
class ProtobufDeserializerIntegrationTest {

  @MockitoBean
  private SecretsManagerClient secretsManagerClient;

  @MockitoBean
  private StatsDClient statsDClient;

  private static final String CORRELATION_ID = "correlation_id";
  private static final String PROTOBUF_TIMESTAMP = "protobuf_timestamp";

  private CountDownLatch kafkaOutputLatch;

  private List<Message<String>> receivedKafkaMessages;

  private Mqtt5BlockingClient mqttPublisher;

  @BeforeEach
  void setup() throws Exception {
    Thread.sleep(3000); // Ensures Kafka broker is up before listener is initialised
    receivedKafkaMessages = new ArrayList<>();
    initialiseMqttPublisher();
  }

  @AfterEach
  void tearDown() {
    mqttPublisher.disconnect();
  }

  @Test
  void consumesEva2MqttMessages_transformsToJson_andDropsBadPayloads() throws Exception {

    kafkaOutputLatch = new CountDownLatch(1);

    // Create response data object based on latest code
    var responseData = TestData.RESPONSE_DATA_V3;

    // Load response data object generated by earlier versions of the code (protobuf 24.x, java 3.25.x)
    // We need this to properly test backwards compatibility
    // (they are both proto 3 messages so should be exactly the same anyway)
    var responseDataFromProto24 = raw.v3_0.ResponseData.parseFrom(
        getBytesFromResource("protomessages/responseDataFull_Schema30_Proto24"));

    // Just check that the proto message generated by earlier code is exactly the same as the current
    assertThat(responseData.toByteArray()).isEqualTo(responseDataFromProto24.toByteArray());

    // Send the bad message first - should get dropped
    sendMqttMessage("bad message".getBytes());
    sendMqttMessage(responseData.toByteArray());
    sendMqttMessage(responseDataFromProto24.toByteArray());
    Thread.sleep(1000);

    boolean completed = kafkaOutputLatch.await(10, TimeUnit.SECONDS);
    assertThat(completed).isTrue();

    long timestamp = Instant.now().toEpochMilli();

    String expectedJson = loadResource("json/converted-message-1.json");

    String receivedMessage1 = receivedKafkaMessages.get(0).getPayload();
    JSONObject receivedJson1 = new JSONObject(receivedMessage1);
    assertAndRemoveTimestamps(receivedJson1, timestamp);
    JSONAssert.assertEquals(expectedJson, receivedJson1.toString(), true);

    String receivedMessage2 = receivedKafkaMessages.get(1).getPayload();
    JSONObject receivedJson2 = new JSONObject(receivedMessage2);
    assertAndRemoveTimestamps(receivedJson2, timestamp);
    JSONAssert.assertEquals(expectedJson, receivedJson2.toString(), true);

    // Counter should be incremented for each message regardless of success / fail
    verify(statsDClient, times(3)).incrementCounter("messages.ingested.total");

    for (Message<String> message : receivedKafkaMessages) {
      assertThat(message.getHeaders().get(CORRELATION_ID)).isNotNull();
      assertThat(message.getHeaders().get(PROTOBUF_TIMESTAMP)).isNotNull();
    }

  }

  @Test
  void consumesEva25MqttMessages_andTransformsToJson() throws Exception {

    kafkaOutputLatch = new CountDownLatch(1);

    var responseData = TestData.RESPONSE_DATA;
    var responseDataFromProto24 = ResponseData.parseFrom(
        getBytesFromResource("protomessages/responseDataFull_Schema31_Proto24"));

    assertThat(responseData).isEqualTo(responseDataFromProto24);

    sendMqttMessage(responseData.toByteArray());
    Thread.sleep(1000);

    boolean completed = kafkaOutputLatch.await(10, TimeUnit.SECONDS);

    assertThat(completed).isTrue();

    long timestamp = Instant.now().toEpochMilli();

    String receivedMessage1 = receivedKafkaMessages.get(0).getPayload();
    JSONObject receivedJson1 = new JSONObject(receivedMessage1);
    assertAndRemoveTimestamps(receivedJson1, timestamp);
    String expectedJson = loadResource("json/converted-message-2.json");
    JSONAssert.assertEquals(expectedJson, receivedJson1.toString(), true);

    verify(statsDClient, times(1)).incrementCounter("messages.ingested.total");

    for (Message<String> message : receivedKafkaMessages) {
      assertThat(message.getHeaders().get(CORRELATION_ID)).isNotNull();
      assertThat(message.getHeaders().get(PROTOBUF_TIMESTAMP)).isNotNull();
    }
  }

  @KafkaListener(topics = outputKafkaTopic, groupId = "${kafka.clientId}-group-id")
  void topicListener(@Payload String payload, @Headers MessageHeaders headers) {
    Message<String> message = MessageBuilder.createMessage(payload, new MessageHeaders(headers));
    receivedKafkaMessages.add(message);
    kafkaOutputLatch.countDown();
  }

  private void initialiseMqttPublisher() {
    mqttPublisher = Mqtt5Client.builder()
        .serverPort(IntegrationTestConfiguration.hiveMqContainer.getMqttPort())
        .identifier("publisher")
        .buildBlocking();

    mqttPublisher.connect();
  }

  private void sendMqttMessage(byte[] message) {
    String mqttInputTopic = "test/topic";
    mqttPublisher.publishWith()
        .topic(mqttInputTopic)
        .payload(message)
        .send();
  }

  private String loadResource(String resource) throws IOException {
    return IOUtils.resourceToString(resource, StandardCharsets.UTF_8,
        ClassLoader.getSystemClassLoader());
  }

  private static byte[] getBytesFromResource(String resource) throws IOException {
    var is = ProtobufDeserializerIntegrationTest.class.getClassLoader()
        .getResourceAsStream(resource);
    var bytes = is.readAllBytes();
    is.close();
    return bytes;
  }

  private void assertAndRemoveTimestamps(JSONObject json, long timestamp) {
    assertThat(json.get("start_deserialisation_timestamp")).isNotNull();
    assertThat((Long) json.get("start_deserialisation_timestamp") < timestamp).isTrue();
    assertThat(json.get("end_deserialisation_timestamp")).isNotNull();
    assertThat((Long) json.get("end_deserialisation_timestamp") < timestamp).isTrue();
    assertThat((Long) json.get("start_deserialisation_timestamp")
        <= (Long) json.get("end_deserialisation_timestamp")).isTrue();
    json.remove("start_deserialisation_timestamp");
    json.remove("end_deserialisation_timestamp");
  }
}
