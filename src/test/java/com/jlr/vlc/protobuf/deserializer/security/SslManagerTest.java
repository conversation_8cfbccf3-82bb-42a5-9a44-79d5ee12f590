/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.security;

import static org.assertj.core.api.Assertions.assertThat;

import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.TrustManagerFactory;
import java.security.PrivateKey;
import java.security.Security;
import java.security.cert.X509Certificate;

class SslManagerTest {

  private static final String SECURITY_PROVIDER = "BC";
  private static final JcaX509CertificateConverter certificateConverter =
      new JcaX509CertificateConverter().setProvider(SECURITY_PROVIDER);
  private static final String MQTT_CERT = """
      -----B<PERSON>IN CERTIFICATE-----
      MIICUTCCAfugAwIBAgIBADANBgkqhkiG9w0BAQQFADBXMQswCQYDVQQGEwJDTjEL
      MAkGA1UECBMCUE4xCzAJBgNVBAcTAkNOMQswCQYDVQQKEwJPTjELMAkGA1UECxMC
      VU4xFDASBgNVBAMTC0hlcm9uZyBZYW5nMB4XDTA1MDcxNTIxMTk0N1oXDTA1MDgx
      NDIxMTk0N1owVzELMAkGA1UEBhMCQ04xCzAJBgNVBAgTAlBOMQswCQYDVQQHEwJD
      TjELMAkGA1UEChMCT04xCzAJBgNVBAsTAlVOMRQwEgYDVQQDEwtIZXJvbmcgWWFu
      ZzBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQCp5hnG7ogBhtlynpOS21cBewKE/B7j
      V14qeyslnr26xZUsSVko36ZnhiaO/zbMOoRcKK9vEcgMtcLFuQTWDl3RAgMBAAGj
      gbEwga4wHQYDVR0OBBYEFFXI70krXeQDxZgbaCQoR4jUDncEMH8GA1UdIwR4MHaA
      FFXI70krXeQDxZgbaCQoR4jUDncEoVukWTBXMQswCQYDVQQGEwJDTjELMAkGA1UE
      CBMCUE4xCzAJBgNVBAcTAkNOMQswCQYDVQQKEwJPTjELMAkGA1UECxMCVU4xFDAS
      BgNVBAMTC0hlcm9uZyBZYW5nggEAMAwGA1UdEwQFMAMBAf8wDQYJKoZIhvcNAQEE
      BQADQQA/ugzBrjjK9jcWnDVfGHlk3icNRq0oV7Ri32z/+HQX67aRfgZu7KWdI+Ju
      Wm7DCfrPNGVwFWUQOmsPue9rZBgO
      -----END CERTIFICATE-----""";
  private static final String MQTT_KEY = """
      -----BEGIN ENCRYPTED PRIVATE KEY-----
      MIIFLTBXBgkqhkiG9w0BBQ0wSjApBgkqhkiG9w0BBQwwHAQIEetsi+sBBOcCAggA
      MAwGCCqGSIb3DQIJBQAwHQYJYIZIAWUDBAEqBBB3wYFLseroeiAnAv4GzBJOBIIE
      0MN3mfhH2BLe/tCZriL16GIAzivG839D6UJCHTlrCWhPp3HgXJFo4KDyVr8M85jw
      TydAkQoSkBK6P9cXMzqMOL0Qt6kIqQYV10K8LaB1P4Rby69iuJ1C12W4mSpPSprx
      s7ieFockRsqLSoOaK+HAGXqC3ScHTK68IajUr6Io5/rYEHaYJoqsViEJQ3aRk77X
      3OE7L5YKTzwzUTjzOQntnfo8Nm2cvizemJXsYzQwtoO8B68eE7S06wRH1f0152it
      w9QKqqomaAeKZuFRXyQpCp9GyvLDV/09yQSpTEBIINnuh2mHYDP1OZK9Xig9XyUb
      Z3SfAKJ9UCNcsab818H8x9fbG6yh8hTBMNt7F6jpGJ502l672sliA0SlSeRoG/UP
      AHnVrUhsWBrNY3OCu72dvGWPPh4bvlimNRHuJ3VsrrBZCh66inNH++tZYc7+C+Z9
      GJns9Dg4ngmYKo7od3ndHuGrbbFbLhXHhJvegdfCAAZ7VBIyI5E7Zbefb8gHr6O3
      VDYZCt5ELy5NFUYONNOQHFvhyzf77kgzbFHp4Rqs2wK84FjSrMc+gdRUvOqW3ZMu
      29Brq03Ce38TlikKZeUZ0I2HwdsuALrxe+GMuoelVBn0w858FES2J1ZwZEqZ6AN9
      MjEsMomWaDdrIw6sqDK4cqD0pSpekybffvkD/T9Iy656yg0Sk+TY8aZQhXzwkDO6
      WrX2peVbpQGxZ8R19/sTGGHDJ0o0hV0Iyz2qCyPiHclJ6gTWMKLhjcmAGrPF8Jyl
      xT9rP0ZCtP1OEzFXDa3wV8sqCxWDqC6oSmEygsGH43LcL5+P495APHy4IdcyI7aP
      XiMtLfXxAZ1CjgNVmP0AsY9DpNVM51DFvXNrrVJpGZkN5SPD49Ky0W5+eBsPsfEO
      0Rf5/zZGAiVCft5JdGrrezCQYKO1PIJwiGYR5aQ3aPizGaX3K+qxTI4bUJBypnwA
      A8IH9y/eeQaBIEAGwerPL/rUiUI1s4QZ19VvPxQIOyBgB9HoN7QCAX2IjoaIoGAS
      +7XxYUeBcg9b9wl5gc1x305yGTKPcbJODdHk0OIeCND7XNxiyzbJg2FKOrx0W0B9
      5D79xdPesOXLbwedVyp8jrqEIRXrjQ2czYWx+ahUZWVJmPw5HXLxIsj23dqpRuBF
      1qF9vaMGWeWLMaOInFAaZOcOTkW19AhECFg9cnfAwATn034hoZjjjIOidQQ6ulVz
      VDmjv/xtezh3MEoyecTFPk09FYLvu94xzpdhpfAxPEXcEO1HhHKR8Ik//aWmTHu8
      KzI0biWnDCO7SD1g+LYK6UcyzpwozVOpUKS2vXRiYW5zsumQcRGBOlIBI7v/osSQ
      s/i7J7HVR+Ic38rHOmQfh1H0jt1TMYrOLnlCHaptSTcRRu2c5aQGE7vGFrrlOAte
      UDe2PAGC0HMLXBcCTBvA1152NZHVRw8KTtiz6zdEmRySq1/ozC9BHL3AGgiGJLJ3
      OBRdYxHpkbUUHlKJ+MTJWTnrzNyZaukg+MJDDkhSITUqIQJ7vpi8zdI/B+zoJgUj
      +GLaomrCMuZZls6bngRCQ4kQnLSu8vtIPSgyl7MBR3O9yTSMeItadwW+EZS4OI5I
      nvSbvvWeMIXN1wglng0RrUiEKZWdAeCA1mzimqPTA/jQ
      -----END ENCRYPTED PRIVATE KEY-----""";

  @BeforeAll
  static void setupAll() {
    Security.addProvider(new BouncyCastleProvider());
  }

  @Test
  void testGetCertFromFile() {
    X509Certificate certificate = getCertificate();
    assertThat(certificate).isNotNull();
    int version = certificate.getVersion();
    assertThat(version).isEqualTo(3);
  }

  @Test
  void testGetKeyFromFile() {
    PrivateKey privateKey = getKey();
    assertThat(privateKey).isNotNull();
  }

  @Test
  void testGetTrustManagerFactory() {
    X509Certificate certificate = getCertificate();
    TrustManagerFactory trustManagerFactory = SslManager.getTrustManagerFactory(certificate);
    assertThat(trustManagerFactory).isNotNull();
  }

  @Test
  void testGetKeyManagerFactory() {
    X509Certificate certificate = getCertificate();
    PrivateKey privateKey = getKey();
    KeyManagerFactory keyManagerFactory =
        SslManager.getKeyManagerFactory(certificate, privateKey, "password");
    assertThat(keyManagerFactory).isNotNull();
  }

  @Test
  void testGetManagerFactories() {
    ManagerFactories managerFactories = SslManager.getManagerFactories(
        MQTT_CERT,
        MQTT_CERT,
        MQTT_KEY,
        "password"
    );
    assertThat(managerFactories).isNotNull();
    assertThat(managerFactories.getKeyManagerFactory()).isNotNull();
    assertThat(managerFactories.getTrustManagerFactory()).isNotNull();
  }

  private X509Certificate getCertificate() {
    return SslManager.getCert(MQTT_CERT, certificateConverter);
  }

  private PrivateKey getKey() {
    return SslManager.getKey(MQTT_KEY, "password");
  }
}