/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.keycloakapiclient;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.jlr.vlc.protobuf.deserializer.config.KeycloakConfig;
import com.jlr.vlc.protobuf.deserializer.error.KeycloakApiClientServiceException;
import com.jlr.vlc.protobuf.deserializer.error.KeycloakApiClientServiceServerException;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.Security;
import org.apache.commons.io.IOUtils;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.bouncycastle.openssl.jcajce.JceOpenSSLPKCS8DecryptorProviderBuilder;
import org.bouncycastle.operator.InputDecryptorProvider;
import org.bouncycastle.operator.OperatorCreationException;
import org.bouncycastle.pkcs.PKCS8EncryptedPrivateKeyInfo;
import org.bouncycastle.pkcs.PKCSException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFunction;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(MockitoExtension.class)
class KeycloakApiClientServiceImplTest {

  @Mock
  private ExchangeFunction exchangeFunction;

  private KeycloakConfig keycloakConfig;

  @BeforeEach
  void setup() {
    keycloakConfig = new KeycloakConfig();
    String clientId = "obg-vehicle-discovery";
    keycloakConfig.setClientID(clientId);
    keycloakConfig.setGrantType("credentials");
    keycloakConfig.setAssertionType("assertionType");
    String password = "changeme";
    keycloakConfig.setPrivateKeyPassword(password);
    keycloakConfig.setPrivateKey(loadResource("keycloakPrivateKey.txt"));
  }

  @Test
  @DisplayName("For retrieving JWT token from Keycloak successfully")
  void testForGetTokenForClientSuccessfully() {
    when(exchangeFunction.exchange(any())).thenReturn(Mono.just(getMockResponse()));

    WebClient webClient = WebClient.builder()
        .exchangeFunction(exchangeFunction)
        .build();
    KeycloakApiClientServiceImpl keycloakApiClientService =
        new KeycloakApiClientServiceImpl(webClient, keycloakConfig);
    KeycloakApiClientServiceImpl spyKeycloakApiClientService =
        Mockito.spy(keycloakApiClientService);

    StepVerifier.create(spyKeycloakApiClientService.getTokenForClient())
        .expectNext("some_token")
        .verifyComplete();
  }

  @Test
  @DisplayName("For Error with HTTP 4xx response while retrieving JWT token from Keycloak")
  void testForGetTokenForClientWith4xxError() {
    when(exchangeFunction.exchange(any())).thenReturn(
        Mono.error(new KeycloakApiClientServiceException(
            HttpStatus.BAD_REQUEST.getReasonPhrase(), HttpStatus.BAD_REQUEST.value())));

    WebClient webClient = WebClient.builder()
        .exchangeFunction(exchangeFunction)
        .build();

    KeycloakApiClientServiceImpl keycloakApiClientService =
        new KeycloakApiClientServiceImpl(webClient, keycloakConfig);
    KeycloakApiClientServiceImpl spyKeycloakApiClientService =
        Mockito.spy(keycloakApiClientService);

    StepVerifier.create(spyKeycloakApiClientService.getTokenForClient())
        .expectError(KeycloakApiClientServiceException.class)
        .verify();

  }

  @Test
  @DisplayName("For Error with HTTP 5xx response while retrieving JWT token from Keycloak")
  void testForGetTokenForClientWith5xxError() {
    when(exchangeFunction.exchange(any())).thenReturn(
        Mono.error(new KeycloakApiClientServiceServerException(
            HttpStatus.INTERNAL_SERVER_ERROR.getReasonPhrase(),
            HttpStatus.INTERNAL_SERVER_ERROR.value())));

    WebClient webClient = WebClient.builder()
        .exchangeFunction(exchangeFunction)
        .build();

    KeycloakApiClientServiceImpl keycloakApiClientService =
        new KeycloakApiClientServiceImpl(webClient, keycloakConfig);
    KeycloakApiClientServiceImpl spyKeycloakApiClientService =
        Mockito.spy(keycloakApiClientService);

    StepVerifier.create(spyKeycloakApiClientService.getTokenForClient())
        .expectError(KeycloakApiClientServiceServerException.class)
        .verify();

  }


  private ClientResponse getMockResponse() {
    return ClientResponse
        .create(HttpStatus.OK)
        .header("Content-Type", "application/json")
        .body("some_token")
        .build();
  }

  private PrivateKey loadDummyKey()
      throws IOException, PKCSException,
      OperatorCreationException {


    Security.addProvider(new BouncyCastleProvider());
    String keyFile = keycloakConfig.getPrivateKey();
    String keyPassword = keycloakConfig.getPrivateKeyPassword();

    try (PEMParser reader = new PEMParser(new FileReader(keyFile)); reader) {
      Object keyObject = reader.readObject();
      JcaPEMKeyConverter converter = new JcaPEMKeyConverter().setProvider("BC");

      JceOpenSSLPKCS8DecryptorProviderBuilder keyConverter =
          new JceOpenSSLPKCS8DecryptorProviderBuilder().setProvider("BC");
      InputDecryptorProvider provider = keyConverter.build(keyPassword.toCharArray());

      PKCS8EncryptedPrivateKeyInfo privateKeyInfo = (PKCS8EncryptedPrivateKeyInfo) keyObject;
      PrivateKeyInfo pkInfo = privateKeyInfo.decryptPrivateKeyInfo(provider);

      return converter.getPrivateKey(pkInfo);


    }
  }

  private static String loadResource(String resource) {
    try {
      return IOUtils.resourceToString(resource, StandardCharsets.UTF_8,
          ClassLoader.getSystemClassLoader());
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }
}
