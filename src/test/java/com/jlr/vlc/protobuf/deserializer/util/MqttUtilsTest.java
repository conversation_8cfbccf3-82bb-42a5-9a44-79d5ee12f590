/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.util;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.hivemq.client.mqtt.datatypes.MqttTopic;
import com.hivemq.client.mqtt.mqtt5.datatypes.Mqtt5UserProperties;
import com.hivemq.client.mqtt.mqtt5.datatypes.Mqtt5UserProperty;
import com.hivemq.client.mqtt.mqtt5.message.publish.Mqtt5Publish;
import org.junit.jupiter.api.Test;

public class MqttUtilsTest {

  private static final String DEFAULT_FLEET_ID = "";
  private static final String DEFAULT_PUBLISH_RECEIVED_TIMESTAMP = "N/A";
  private Mqtt5Publish mockMessage = mock(Mqtt5Publish.class);

  @Test
  void getFleetId_shouldReturnDefault_whenNoMqttUserProperties() {
    when(mockMessage.getUserProperties()).thenReturn(Mqtt5UserProperties.of());
    var result = MqttUtils.getFleetIdOrElse(mockMessage, DEFAULT_FLEET_ID);
    assertThat(result).isEqualTo(DEFAULT_FLEET_ID);
  }

  @Test
  void getFleetId_shouldReturnDefault_whenNoFleetIdUserProperty() {
    when(mockMessage.getUserProperties()).thenReturn(Mqtt5UserProperties.of(
        Mqtt5UserProperty.of("otherProperty", "value")));
    var result = MqttUtils.getFleetIdOrElse(mockMessage, DEFAULT_FLEET_ID);
    assertThat(result).isEqualTo(DEFAULT_FLEET_ID);
  }

  @Test
  void getFleetId_shouldReturnFleetIdUserProperty_whenExists() {
    when(mockMessage.getUserProperties()).thenReturn(Mqtt5UserProperties.of(
        Mqtt5UserProperty.of("fleetId", "value")));
    var result = MqttUtils.getFleetIdOrElse(mockMessage, DEFAULT_FLEET_ID);
    assertThat(result).isEqualTo("value");
  }

  @Test
  void getPublishReceivedTimestamp_shouldReturnDefault_whenNoMqttUserProperties() {
    when(mockMessage.getUserProperties()).thenReturn(Mqtt5UserProperties.of());
    var result = MqttUtils.getPublishReceivedTimestampOrElse(mockMessage,
        DEFAULT_PUBLISH_RECEIVED_TIMESTAMP);
    assertThat(result).isEqualTo(DEFAULT_PUBLISH_RECEIVED_TIMESTAMP);
  }

  @Test
  void getPublishReceivedTimestamp_shouldReturnDefault_whenNoPublishReceivedTimestampUserProperty() {
    when(mockMessage.getUserProperties()).thenReturn(Mqtt5UserProperties.of(
        Mqtt5UserProperty.of("otherProperty", "value")));
    var result = MqttUtils.getPublishReceivedTimestampOrElse(mockMessage,
        DEFAULT_PUBLISH_RECEIVED_TIMESTAMP);
    assertThat(result).isEqualTo(DEFAULT_PUBLISH_RECEIVED_TIMESTAMP);
  }

  @Test
  void getPublishReceivedTimestamp_shouldReturnPublishReceivedTimestampUserProperty_whenExists() {
    when(mockMessage.getUserProperties()).thenReturn(Mqtt5UserProperties.of(
        Mqtt5UserProperty.of("publish_received_timestamp", "value")));
    var result = MqttUtils.getPublishReceivedTimestampOrElse(mockMessage,
        DEFAULT_PUBLISH_RECEIVED_TIMESTAMP);
    assertThat(result).isEqualTo("value");
  }

  @Test
  void getVehicleId_shouldReturnTopicLevel2_whenEva2Topic() {

    final var mqttTopicEva2 = MqttTopic.builder()
        .addLevel("dt").addLevel("b6b07467-9099-4e84-8d09-38161c70dcb9")
        .addLevel("stream").addLevel("raw")
        .build();

    when(mockMessage.getTopic()).thenReturn(mqttTopicEva2);
    var result = MqttUtils.getVehicleId(mockMessage);
    assertThat(result).isEqualTo("b6b07467-9099-4e84-8d09-38161c70dcb9");
  }

  @Test
  void getVehicleId_shouldReturnTopicLevel2WithPrefixRemoved_whenEva25Topic() {

    final var mqttTopicEva25 = MqttTopic.builder()
        .addLevel("vehicle").addLevel("b6b07467-9099-4e84-8d09-38161c70dcb9_vcm").addLevel("em")
        .addLevel("data").addLevel("stream")
        .addLevel("pre")
        .build();

    when(mockMessage.getTopic()).thenReturn(mqttTopicEva25);
    var result = MqttUtils.getVehicleId(mockMessage);
    assertThat(result).isEqualTo("b6b07467-9099-4e84-8d09-38161c70dcb9");
  }
}
