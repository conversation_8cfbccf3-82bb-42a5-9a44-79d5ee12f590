spring:
  application:
    name: protobuf-deserializer

logging:
  level:
    root: INFO
    com.jlr.vlc.protobuf.deserializer: INFO

server:
  port: 8089

mqtt:
  automaticReconnect: true
  cleanSession: false
  retain: false
  connectionTimeout: 10
  keepAliveInterval: 120
  clientId: ${POD_NAME}
  protocol: ssl
  hostname: ${HIVEMQ_HOSTNAME}
  port: ${HIVEMQ_PORT}
  qosLevel: 1
  groupId: ${spring.application.name}
  topics:
    inboundRawTopic: $share/${mqtt.groupId}/dt/+/stream/raw
    inboundEngTopic: $share/${mqtt.groupId}/dt/+/stream/eng
    inboundDiagTopic: $share/${mqtt.groupId}/dt/+/stream/diag
    newInboundRawTopic: $share/${mqtt.groupId}/vehicle/+/va/data/stream/raw
    newInboundDiagTopic: $share/${mqtt.groupId}/vehicle/+/va/data/stream/diag
    inboundPreTopic: $share/${mqtt.groupId}/vehicle/+/va/data/stream/pre

hivemq:
  eseEnabled: ${HIVE_ESE_ENABLED}

statsd:
  hostname: ${DD_AGENT_HOST}
  port: 8125

kafka:
  bootStrapServers: ${MSK_BOOTSTRAP_SERVERS}
  clientId: ${spring.application.name}
  retryBackoff: 10000 # in ms, Must be Long compatible
  maxInFlightRequests: 10 # int, default is 5, make higher if send acks take to long to come back
  acksConfig: all
  securityProtocol: SASL_SSL
  saslMechanism: ${MSK_SASL_MECHANISM}
  jaasConfig: ${MSK_JAAS_CONFIG}
  clientCallback: ${MSK_CLIENT_CALLBACK}
  outboundTopic: LIVE.data-product-factory.input

ssl:
  password: ${HIVEMQ_PASSWORD}
  mqttClientCa: ${MQTT_CLIENT_CA}
  mqttClientCert: ${MQTT_CLIENT_CERT}
  mqttClientKey: ${MQTT_CLIENT_KEY}

keycloak:
  authTokenUri: ${KEYCLOAK_HOSTNAME}
  clientID: ${CLIENT_ID}
  privateKeyPassword: ${KEYCLOAK_PASSPHRASE}
  privateKey: ${KEYCLOAK_PRIVATE_KEY}
  grantType: client_credentials
  assertionType: urn:ietf:params:oauth:client-assertion-type:jwt-bearer

isUpdatedHiveMqCert: ${IS_UPDATED_HIVE_MQ_CERT:false}

mqtt-client-key-secret: ${MQTT_CLIENT_KEY_SECRET:""}
mqtt-client-passphrase-secret: ${MQTT_CLIENT_PASSPHRASE_SECRET:""}
mqtt-client-cert-secret: ${MQTT_CLIENT_CERT_SECRET:""}
