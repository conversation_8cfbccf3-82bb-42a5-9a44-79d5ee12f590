spring:
  config:
    import:
      - aws-secretsmanager:protobuf-deserializer-app
      - aws-secretsmanager:HIVEMQ_CA

logging:
  level:
    com.jlr.vlc.protobuf.deserializer: DEBUG
  throttle:
    enabled: true
    frequency: 15

hivemq:
  eseEnabled: true

mqtt:
  topics:
    inboundEmDiagTopic: $share/${mqtt.groupId}/vehicle/+/em/data/stream/diag
    inboundEmPreTopic: $share/${mqtt.groupId}/vehicle/+/em/data/stream/pre

IS_UPDATED_HIVE_MQ_CERT: true

ssl:
  mqttClientCa: ${HIVEMQ_CA}
