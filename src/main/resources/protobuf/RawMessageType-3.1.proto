syntax = "proto3";

package jlr.va;

option java_package = "com.jlr.vlc.protobuf.deserializer.protocol.raw.v3";
option java_outer_classname = "RawProtobufMessage";
option java_multiple_files = true;

message ResponseData {
  message dataType {
    oneof value_oneof {
      uint64 data_uint = 1;
      float data_float = 2;
      bytes data_byte = 3;
      string data_string = 4;
    }
  }

  message GlobalRT {
    uint64 RT_value = 1;
    uint64 RT_timestamp_ms = 2;
  }

  message Data {
    message Samples {
      uint64 timestamp_ms = 1;
      dataType sample = 2;
    }

    message CalculatedData {
      uint64 end_time_ms = 1;
      uint32 duration_ms = 2;
      float avg = 3;
      dataType min = 4;
      dataType max = 5;
      float std_dev = 6;
      uint32 count = 7;
    }

    string data_id = 1;
    repeated Samples samples = 2;
    CalculatedData calc = 3;
  }

  string query_id = 1;
  uint64 event_timestamp_ms = 2;
  repeated Data data = 3;
  GlobalRT globalRT = 4;
  uint64 emit_timestamp_ms = 5;
  string session_powercycle = 6;

}
