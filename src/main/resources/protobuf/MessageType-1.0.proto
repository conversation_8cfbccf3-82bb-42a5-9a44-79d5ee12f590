syntax = "proto2";

package jlr.queue.protobuf;

option java_package = "com.jlr.dda.iotbroker.datatransform.protocol";
option java_outer_classname = "ProtobufMessage";
option java_multiple_files = true;

message preProcessedData {
  required int32 requestId = 1;
  optional int64 timeStamp = 2;

  message SIGNALVALUES {
    required double id = 1;
    required double value = 2;
    optional int64 timeStamp = 3;
  }

  repeated SIGNALVALUES signalValues = 3;
}
