syntax = "proto3";

package jlr.queue.protobuf;

option java_package = "com.jlr.dda.iotbroker.datatransform.protocol.raw.v2";
option java_outer_classname = "RawProtobufMessage";
option java_multiple_files = true;

message ResponseData {

  /* CollectedData can hold signal data OR diagnostics data like DIDs/DTCs
   * This message will be repeated for multiple signals / DIDs / DTCs */
  message CollectedData {
    uint64 timestamp = 1;
    bytes value = 2;
  }

  /* CalculatedData can hold results of on-board calculations.
   * First 2 fields define the time window for which calculations are performed
   * Remaining fields hold result of the calculations */
  message CalculatedData {
    uint64 end_time = 1;
    uint64 duration = 2;
    bytes avg = 3;
    bytes min = 4;
    bytes max = 5;
    bytes std_dev = 6;
    bytes median = 7;
    bytes mode = 8;
    bytes count = 9;
  }

  uint64 request_id = 1;
  repeated CollectedData data = 2;
  CalculatedData derived_data = 3;
}