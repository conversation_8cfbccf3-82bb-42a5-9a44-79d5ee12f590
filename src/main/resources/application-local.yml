spring:
  application:
    name: protobuf-deserializer-local

logging:
  level:
    root: INFO
    com.jlr.vlc.protobuf.deserializer: DEBUG
  throttle:
    enabled: false
    frequency: 1000

server:
  port: 8089

mqtt:
  automaticReconnect: true
  cleanSession: false
  retain: false
  connectionTimeout: 10
  keepAliveInterval: 120
  clientId: protobuf-deserializer-local
  protocol: non-ssl
  hostname: localhost
  port: 1883
  qosLevel: 1
  groupId: ${spring.application.name}
  topics:
    inboundEmDiagTopic: $share/${mqtt.groupId}/vehicle/+/em/data/stream/diag
    inboundEmPreTopic: $share/${mqtt.groupId}/vehicle/+/em/data/stream/pre

hivemq:
  eseEnabled: false

kafka:
  bootStrapServers: localhost:9092
  clientId: ${spring.application.name}
  retryBackoff: 10000 # in ms, Must be Long compatible
  maxInFlightRequests: 10 # int, default is 5, make higher if send acks take to long to come back
  acksConfig: all
  securityProtocol: PLAINTEXT
  outboundTopic: LOCAL.data-product-factory.input

keycloak:
