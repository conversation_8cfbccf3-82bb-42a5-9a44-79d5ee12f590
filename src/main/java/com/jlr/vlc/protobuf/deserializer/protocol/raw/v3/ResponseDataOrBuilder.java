// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: RawMessageType-3.2.proto
// Protobuf Java Version: 4.29.3

package com.jlr.vlc.protobuf.deserializer.protocol.raw.v3;

public interface ResponseDataOrBuilder extends
    // @@protoc_insertion_point(interface_extends:jlr.va.ResponseData)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string query_id = 1;</code>
   * @return The queryId.
   */
  java.lang.String getQueryId();
  /**
   * <code>string query_id = 1;</code>
   * @return The bytes for queryId.
   */
  com.google.protobuf.ByteString
      getQueryIdBytes();

  /**
   * <code>uint64 event_timestamp_ms = 2;</code>
   * @return The eventTimestampMs.
   */
  long getEventTimestampMs();

  /**
   * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
   */
  java.util.List<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data> 
      getDataList();
  /**
   * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
   */
  com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data getData(int index);
  /**
   * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
   */
  int getDataCount();
  /**
   * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
   */
  java.util.List<? extends com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.DataOrBuilder> 
      getDataOrBuilderList();
  /**
   * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
   */
  com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.DataOrBuilder getDataOrBuilder(
      int index);

  /**
   * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
   * @return Whether the globalRT field is set.
   */
  boolean hasGlobalRT();
  /**
   * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
   * @return The globalRT.
   */
  com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT getGlobalRT();
  /**
   * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
   */
  com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRTOrBuilder getGlobalRTOrBuilder();

  /**
   * <code>uint64 emit_timestamp_ms = 5;</code>
   * @return The emitTimestampMs.
   */
  long getEmitTimestampMs();

  /**
   * <code>string session_powercycle = 6;</code>
   * @return The sessionPowercycle.
   */
  java.lang.String getSessionPowercycle();
  /**
   * <code>string session_powercycle = 6;</code>
   * @return The bytes for sessionPowercycle.
   */
  com.google.protobuf.ByteString
      getSessionPowercycleBytes();
}
