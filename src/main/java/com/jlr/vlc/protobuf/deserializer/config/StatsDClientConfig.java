/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.config;

import com.timgroup.statsd.NonBlockingStatsDClientBuilder;
import com.timgroup.statsd.StatsDClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class StatsDClientConfig {

  @Bean
  public StatsDClient statsDClient(StatsDProperties statsDProperties) {
    return new NonBlockingStatsDClientBuilder()
            .prefix("statsd")
            .hostname(statsDProperties.getHostname())
            .port(statsDProperties.getPort())
            .build();
  }
}
