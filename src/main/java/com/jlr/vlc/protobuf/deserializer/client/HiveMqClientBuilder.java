/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.hivemq.client.mqtt.MqttClientSslConfig;
import com.hivemq.client.mqtt.MqttClientTransportConfig;
import com.hivemq.client.mqtt.mqtt5.Mqtt5Client;
import com.hivemq.client.mqtt.mqtt5.Mqtt5ClientBuilder;
import com.hivemq.client.mqtt.mqtt5.lifecycle.Mqtt5ClientDisconnectedContext;
import com.hivemq.client.mqtt.mqtt5.message.connect.Mqtt5Connect;
import com.hivemq.client.mqtt.mqtt5.reactor.Mqtt5ReactorClient;
import com.hivemq.client.util.TypeSwitch;
import com.jlr.vlc.protobuf.deserializer.config.FeatureToggleConfig;
import com.jlr.vlc.protobuf.deserializer.config.MqttConfig;
import com.jlr.vlc.protobuf.deserializer.config.SecretsHandlerService;
import com.jlr.vlc.protobuf.deserializer.config.SslConfig;
import com.jlr.vlc.protobuf.deserializer.keycloakapiclient.KeycloakApiClientService;
import com.jlr.vlc.protobuf.deserializer.security.ManagerFactories;
import com.jlr.vlc.protobuf.deserializer.security.SslManager;
import java.util.Base64;
import java.util.concurrent.TimeUnit;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.TrustManagerFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import reactor.core.publisher.Mono;

@Slf4j
@Configuration
@Profile({"!local & !test"})
@RequiredArgsConstructor
public class HiveMqClientBuilder {

  private static final String TOKEN_KEYWORD = "access_token";

  private final KeycloakApiClientService keycloakApiClient;

  private final SecretsHandlerService secretsHandlerService;

  private final FeatureToggleConfig featureToggleConfig;

  static MqttClientSslConfig getSslConfig(
      KeyManagerFactory keyManagerFactory,
      TrustManagerFactory trustManagerFactory) {
    return MqttClientSslConfig.builder()
        .keyManagerFactory(keyManagerFactory)
        .trustManagerFactory(trustManagerFactory)
        .build();
  }

  static MqttClientTransportConfig getTransportConfig(
      MqttConfig mqttConfig,
      MqttClientSslConfig sslConfig) {
    return MqttClientTransportConfig.builder()
        .serverHost(mqttConfig.getHostname()).serverPort(mqttConfig.getPort())
        .sslConfig(sslConfig)
        .socketConnectTimeout(mqttConfig.getConnectionTimeout(), TimeUnit.SECONDS).build();
  }

  @Bean
  public Mqtt5ReactorClient mqtt5Client(MqttConfig mqttConfig, SslConfig ssl) {

    ManagerFactories managerFactories = getManagerFactories(ssl);

    if (managerFactories == null) {
      throw new IllegalStateException("Error getting SSL details for Hive Reactor client");
    }

    MqttClientSslConfig sslConfig = getSslConfig(
        managerFactories.getKeyManagerFactory(),
        managerFactories.getTrustManagerFactory()
    );

    MqttClientTransportConfig transportConfig = getTransportConfig(mqttConfig, sslConfig);

    return getAccessToken()
        .switchIfEmpty(Mono.error(new Exception("Unable to get access token")))
        .map(token -> buildReactorClient(token, transportConfig, mqttConfig)).block();
  }

  Mqtt5ReactorClient buildReactorClient(
      String token,
      MqttClientTransportConfig transportConfig,
      MqttConfig mqttConfig) {
    Mqtt5Client client = buildClient(token, transportConfig, mqttConfig);
    return Mqtt5ReactorClient.from(client);
  }

  Mqtt5Client buildClient(
      String token,
      MqttClientTransportConfig transportConfig,
      MqttConfig mqttConfig) {
    Mqtt5ClientBuilder clientBuilder = Mqtt5Client.builder();
    clientBuilder = clientBuilder.identifier(mqttConfig.getClientId())
        .transportConfig(transportConfig)
        .simpleAuth()
        .username(mqttConfig.getClientId())
        .password(token.getBytes())
        .applySimpleAuth();

    if (mqttConfig.isAutomaticReconnect()) {
      clientBuilder.automaticReconnectWithDefaultConfig()
          .addDisconnectedListener(context ->
              TypeSwitch.when(context).is(Mqtt5ClientDisconnectedContext.class,
                  context5 -> context5.getReconnector()
                      .connectWith().simpleAuth()
                      .password(waitForAccessToken())
                      .applySimpleAuth()
                      .applyConnect()));
    }
    return clientBuilder.build();
  }

  static String decodeBase64String(String sslCert) {
    return new String(Base64.getDecoder().decode(sslCert));
  }

  private byte[] waitForAccessToken() {
    return getAccessToken()
        .switchIfEmpty(Mono.error(new Exception("Unable to get access token on reconnect")))
        .map(String::getBytes)
        .block();
  }

  private Mono<String> getAccessToken() {
    return keycloakApiClient.getTokenForClient()
        .flatMap(tokenPayload -> {
          try {
            return Mono.just(new ObjectMapper().readTree(tokenPayload).get(TOKEN_KEYWORD).asText());
          } catch (JsonProcessingException exception) {
            log.error("Failed to process JSON Object", exception);
            return Mono.empty();
          }
        });
  }

  @Bean
  public Mqtt5Connect mqtt5Connect(MqttConfig mqttConfig) {
    return Mqtt5Connect.builder()
        .cleanStart(mqttConfig.isCleanSession())
        .noSessionExpiry()
        .keepAlive(mqttConfig.getKeepAliveInterval()).build();
  }

  private ManagerFactories getManagerFactories(SslConfig ssl) {
    if (featureToggleConfig.isUpdatedHiveMqCert()) {
      return SslManager.getManagerFactories(
          decodeBase64String(ssl.getMqttClientCa()),
          secretsHandlerService.getMqttClientCertSecretValue(),
          secretsHandlerService.getMqttClientKeySecretValue(),
          secretsHandlerService.getMqttClientPassphraseSecretValue()
      );
    }

    return SslManager.getManagerFactories(
        decodeBase64String(ssl.getMqttClientCa()),
        decodeBase64String(ssl.getMqttClientCert()),
        decodeBase64String(ssl.getMqttClientKey()),
        ssl.getPassword()
    );
  }

}
