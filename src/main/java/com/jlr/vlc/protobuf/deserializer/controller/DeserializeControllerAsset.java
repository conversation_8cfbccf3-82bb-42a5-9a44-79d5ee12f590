/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.controller;

import com.google.protobuf.Descriptors;
import com.hivemq.client.mqtt.datatypes.MqttTopic;
import com.hivemq.client.mqtt.mqtt5.message.publish.Mqtt5Publish;
import com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData;
import com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data;
import com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData;
import com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples;
import com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType;
import com.jlr.vlc.protobuf.deserializer.util.MqttUtils;
import com.jlr.vlc.protobuf.deserializer.util.ProtobufUtils;
import datadog.trace.api.Trace;
import java.time.Instant;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;


@Slf4j
@Primary
@Component
@RequiredArgsConstructor
public class DeserializeControllerAsset implements Function<Mqtt5Publish, JSONObject> {
  private static final String VALUE = "value";

  @SneakyThrows
  @Override
  @Trace(operationName = "DeserializerControllerAsset.apply", resourceName = "data.deserialize")
  public JSONObject apply(Mqtt5Publish message) {
    final long startTimestamp = Instant.now().toEpochMilli();
    MqttTopic topic = message.getTopic();
    byte[] payload = message.getPayloadAsBytes();

    final String fleetId = MqttUtils.getFleetIdOrElse(message, "");

    if (log.isDebugEnabled()) {
      log.debug("INCOMING PROTOBUF SIGNAL: {} ON TOPIC: {} RECEIVED BY BROKER AT: {}",
          payload,
          topic,
          MqttUtils.getPublishReceivedTimestampOrElse(message, "N/A")
      );
    }

    final var vehicleId = MqttUtils.getVehicleId(message);
    final var responseData = ResponseData.parseFrom(payload);
    return processVaProtoBufData(vehicleId, fleetId, responseData, startTimestamp);
  }


  private JSONObject processVaProtoBufData(String uniqueId, String fleetId,
                                           ResponseData responseData, long startTimestamp) {
    JSONObject vaDataJson = new JSONObject();
    vaDataJson.put("query_id", responseData.getQueryId());
    vaDataJson.put("event_timestamp_ms", responseData.getEventTimestampMs());
    vaDataJson.put("emit_timestamp_ms", responseData.getEmitTimestampMs());
    vaDataJson.put("session_powercycle", responseData.getSessionPowercycle());
    // TODO - do we need to strip _tcua / _vcm if eva25 ?
    vaDataJson.put("unique_id", uniqueId);
    vaDataJson.put("fleet_id", fleetId);
    vaDataJson.put("start_deserialisation_timestamp", startTimestamp);

    constructGlobalRT(vaDataJson, responseData);

    JSONArray dataArray = new JSONArray();

    for (Data data : responseData.getDataList()) {
      JSONObject dataObject = new JSONObject();
      dataObject.put("data_id", data.getDataId());
      constructSamples(dataObject, data);
      constructCalculatedData(dataObject, data);
      dataArray.put(dataObject);
    }
    JSONObject deserializedPayload = vaDataJson.put("data", dataArray);
    log.debug("Deserialized JSON for - {}: {}", uniqueId, deserializedPayload);
    return deserializedPayload;
  }

  private void constructSamples(JSONObject dataObject, Data data) {
    List<Samples> sampleList = data.getSamplesList();
    JSONArray sampleArray = new JSONArray();
    for (Samples samples : sampleList) {
      JSONObject sampleObj = new JSONObject();
      sampleObj.put("timestamp_ms", samples.getTimestampMs());
      sampleObj.put("type", samples.getSample().getValueOneofCase().toString());
      sampleObj.put(VALUE, ProtobufUtils.getOneOfValue(samples.getSample()));
      sampleArray.put(sampleObj);
    }
    dataObject.put("samples", sampleArray);
  }


  private void constructGlobalRT(JSONObject vaDataJson, ResponseData responseData) {
    if (responseData.hasGlobalRT()) {
      JSONObject globalRT = new JSONObject();
      globalRT.put("RT_value", responseData.getGlobalRT().getRTValue());
      globalRT.put("RT_timestamp_ms", responseData.getGlobalRT().getRTTimestampMs());
      vaDataJson.put("globalRT", globalRT);
    }
  }

  private void constructCalculatedData(JSONObject dataObject, Data data) {
    if (data.hasCalc()) {
      CalculatedData calcData = data.getCalc();
      JSONObject calcObj = new JSONObject();

      List<Descriptors.FieldDescriptor> availableFields =
          ResponseData.Data.CalculatedData.getDescriptor()
              .getFields().stream().filter(calcData::hasField)
              .collect(Collectors.toList());

      for (Descriptors.FieldDescriptor fieldDesc : availableFields) {
        switch (fieldDesc.getName().toLowerCase()) {

          case "end_time_ms":
            calcObj.put("end_time_ms", (long) calcData.getField(fieldDesc));
            break;

          case "duration_ms":
            calcObj.put("duration_ms", (int) calcData.getField(fieldDesc));
            break;

          case "avg":
            calcObj.put("avg", (float) calcData.getField(fieldDesc));
            break;

          case "min":
            dataType min = (dataType) calcData.getField(fieldDesc);
            JSONObject minObj = new JSONObject();
            minObj.put("type", min.getValueOneofCase().toString());
            minObj.put(VALUE, ProtobufUtils.getOneOfValue(min));
            calcObj.put("min", minObj);
            break;

          case "max":
            dataType max = (dataType) calcData.getField(fieldDesc);
            JSONObject maxObj = new JSONObject();
            maxObj.put("type", max.getValueOneofCase().toString());
            maxObj.put(VALUE, ProtobufUtils.getOneOfValue(max));
            calcObj.put("max", maxObj);
            break;

          case "std_dev":
            calcObj.put("std_dev", (float) calcData.getField(fieldDesc));
            break;

          case "count":
            calcObj.put("count", (int) calcData.getField(fieldDesc));
            break;

          default:
            break;
        }
      }
      dataObject.put("calc", calcObj);
    }
  }
}
