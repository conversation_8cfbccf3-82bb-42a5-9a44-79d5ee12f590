/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

@Data
@Configuration
@ConfigurationProperties(prefix = "keycloak")
public class KeycloakConfig {

  private String authTokenUri;

  private String privateKeyPassword;

  private String privateKey;

  private String clientID;

  private String assertionType;

  private String grantType;

  @Bean
  public WebClient webClient() {
    return WebClient.create();
  }
}