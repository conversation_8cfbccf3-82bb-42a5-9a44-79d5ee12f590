/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.security;

import java.io.IOException;
import java.io.StringReader;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Security;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import javax.net.ssl.KeyManagerFactory;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.cert.X509CertificateHolder;
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.bouncycastle.openssl.jcajce.JceOpenSSLPKCS8DecryptorProviderBuilder;
import org.bouncycastle.operator.InputDecryptorProvider;
import org.bouncycastle.pkcs.PKCS8EncryptedPrivateKeyInfo;


@Slf4j
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class SslManager {
  private static final String SECURITY_PROVIDER = "BC";
  private static final String CA_CERT_ALIAS = "ca-certificate";
  private static final String CLI_CERT_ALIAS = "certificate";
  private static final String CLI_KEY_ALIAS = "private-key";

  public static ManagerFactories getManagerFactories(
      final String mqttClientCa,
      final String mqttClientCert,
      final String mqttClientKey,
      final String sslPassword
  ) {
    Security.addProvider(new BouncyCastleProvider());
    JcaX509CertificateConverter certificateConverter =
        new JcaX509CertificateConverter().setProvider(SECURITY_PROVIDER);

    X509Certificate caCert = getCert(mqttClientCa, certificateConverter);
    X509Certificate clientCert = getCert(mqttClientCert, certificateConverter);
    PrivateKey clientKey = getKey(mqttClientKey, sslPassword);

    try {

      TrustManagerFactory trustManagerFactory = getTrustManagerFactory(caCert);
      KeyManagerFactory keyManagerFactory = getKeyManagerFactory(clientCert, clientKey, sslPassword);

      if (keyManagerFactory != null && trustManagerFactory != null) {
        SSLContext context = SSLContext.getInstance("TLSv1.2");
        context.init(
            keyManagerFactory.getKeyManagers(),
            trustManagerFactory.getTrustManagers(),
            null
        );
        return new ManagerFactories(keyManagerFactory, trustManagerFactory, context);
      } else {
        throw new IllegalStateException("Either clientKey manager or trust manager factory is null");
      }

    } catch (NoSuchAlgorithmException e) {
      log.error("No such algorithm error", e);
    } catch (KeyManagementException e) {
      log.error("Key management error ", e);
    }

    return null;
  }

  static TrustManagerFactory getTrustManagerFactory(X509Certificate caCert) {
    try {
      KeyStore caKeyStore = KeyStore.getInstance(KeyStore.getDefaultType());
      caKeyStore.load(null, null);
      caKeyStore.setCertificateEntry(CA_CERT_ALIAS, caCert);

      TrustManagerFactory trustManagerFactory =
          TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
      trustManagerFactory.init(caKeyStore);
      return trustManagerFactory;
    } catch (Exception e) {
      log.error("Error getting SSL context", e);
    }
    return null;
  }

  static KeyManagerFactory getKeyManagerFactory(
      X509Certificate cert,
      PrivateKey key,
      String password) {
    try {
      KeyStore clientKeyStore = KeyStore.getInstance(KeyStore.getDefaultType());
      clientKeyStore.load(null, null);
      clientKeyStore.setCertificateEntry(CLI_CERT_ALIAS, cert);
      clientKeyStore.setKeyEntry(CLI_KEY_ALIAS, key, password.toCharArray(),
          new Certificate[]{cert});
      KeyManagerFactory keyManagerFactory
          = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm());
      keyManagerFactory.init(clientKeyStore, password.toCharArray());
      return keyManagerFactory;
    } catch (Exception e) {
      log.error("Error getting SSL context", e);
    }
    return null;
  }


  static X509Certificate getCert(String cert, JcaX509CertificateConverter certificateConverter) {
    try (PEMParser reader = new PEMParser(new StringReader(cert))) {
      X509CertificateHolder caCertHolder = (X509CertificateHolder) reader.readObject();
      return certificateConverter.getCertificate(caCertHolder);
    } catch (IOException e) {
      log.error("Problem parsing cert string to PEM ", e);
    } catch (CertificateException e) {
      log.error("Problem converting certificate ", e);
    }

    return null;
  }

  static PrivateKey getKey(String mqttClientKey, String password) {
    try (PEMParser reader = new PEMParser(new StringReader(mqttClientKey))) {
      Object keyObject = reader.readObject();
      JcaPEMKeyConverter converter = new JcaPEMKeyConverter().setProvider(SECURITY_PROVIDER);
      JceOpenSSLPKCS8DecryptorProviderBuilder keyConverter =
          new JceOpenSSLPKCS8DecryptorProviderBuilder().setProvider(SECURITY_PROVIDER);
      InputDecryptorProvider provider = keyConverter.build(password.toCharArray());

      PKCS8EncryptedPrivateKeyInfo privateKeyInfo = (PKCS8EncryptedPrivateKeyInfo) keyObject;
      PrivateKeyInfo info = privateKeyInfo.decryptPrivateKeyInfo(provider);
      return converter.getPrivateKey(info);
    } catch (Exception e) {
      log.error("Error getting SSL Context", e);
    }
    return null;
  }

}
