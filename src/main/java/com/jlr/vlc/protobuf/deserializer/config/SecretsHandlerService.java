/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.services.secretsmanager.SecretsManagerClient;
import software.amazon.awssdk.services.secretsmanager.model.GetSecretValueRequest;

@Service
public class SecretsHandlerService {

  private final String mqttClientKeySecret;
  private final String mqttClientPassphraseSecret;
  private final String mqttClientCertSecret;
  private final SecretsManagerClient secretsManagerClient;

  @Autowired
  public SecretsHandlerService(
      @Value("${mqtt-client-key-secret}") String mqttClientKeySecret,
      @Value("${mqtt-client-passphrase-secret}") String mqttClientPassphraseSecret,
      @Value("${mqtt-client-cert-secret}") String mqttClientCertSecret,
      SecretsManagerClient secretsManagerClient
  ) {

    this.mqttClientKeySecret = mqttClientKeySecret;
    this.mqttClientPassphraseSecret = mqttClientPassphraseSecret;
    this.mqttClientCertSecret = mqttClientCertSecret;
    this.secretsManagerClient = secretsManagerClient;
  }

  public String getMqttClientKeySecretValue() {

    final var request = GetSecretValueRequest.builder()
        .secretId(mqttClientKeySecret)
        .build();

    return secretsManagerClient.getSecretValue(request).secretString();
  }

  public String getMqttClientPassphraseSecretValue() {

    final var request = GetSecretValueRequest.builder()
        .secretId(mqttClientPassphraseSecret)
        .build();

    return secretsManagerClient.getSecretValue(request).secretString();
  }

  public String getMqttClientCertSecretValue() {

    final var request = GetSecretValueRequest.builder()
        .secretId(mqttClientCertSecret)
        .build();

    return secretsManagerClient.getSecretValue(request).secretString();
  }
}
