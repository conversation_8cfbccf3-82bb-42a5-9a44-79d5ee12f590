// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: RawMessageType-3.2.proto
// Protobuf Java Version: 4.29.3

package com.jlr.vlc.protobuf.deserializer.protocol.raw.v3;

public final class RawProtobufMessage {
  private RawProtobufMessage() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 3,
      /* suffix= */ "",
      RawProtobufMessage.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_jlr_va_ResponseData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_jlr_va_ResponseData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_jlr_va_ResponseData_dataType_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_jlr_va_ResponseData_dataType_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_jlr_va_ResponseData_GlobalRT_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_jlr_va_ResponseData_GlobalRT_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_jlr_va_ResponseData_Data_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_jlr_va_ResponseData_Data_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_jlr_va_ResponseData_Data_Samples_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_jlr_va_ResponseData_Data_Samples_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_jlr_va_ResponseData_Data_CalculatedData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_jlr_va_ResponseData_Data_CalculatedData_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\030RawMessageType-3.2.proto\022\006jlr.va\"\376\006\n\014R" +
      "esponseData\022\020\n\010query_id\030\001 \001(\t\022\032\n\022event_t" +
      "imestamp_ms\030\002 \001(\004\022\'\n\004data\030\003 \003(\0132\031.jlr.va" +
      ".ResponseData.Data\022/\n\010globalRT\030\004 \001(\0132\035.j" +
      "lr.va.ResponseData.GlobalRT\022\031\n\021emit_time" +
      "stamp_ms\030\005 \001(\004\022\032\n\022session_powercycle\030\006 \001" +
      "(\t\032\337\001\n\010dataType\022\023\n\tdata_uint\030\001 \001(\004H\000\022\024\n\n" +
      "data_float\030\002 \001(\002H\000\022\023\n\tdata_byte\030\003 \001(\014H\000\022" +
      "\025\n\013data_string\030\004 \001(\tH\000\022\025\n\013data_uint32\030\005 " +
      "\001(\rH\000\022\023\n\tdata_bool\030\006 \001(\010H\000\022\024\n\ndata_int32" +
      "\030\007 \001(\005H\000\022\024\n\ndata_int64\030\010 \001(\003H\000\022\025\n\013data_d" +
      "ouble\030\t \001(\001H\000B\r\n\013value_oneof\0325\n\010GlobalRT" +
      "\022\020\n\010RT_value\030\001 \001(\004\022\027\n\017RT_timestamp_ms\030\002 " +
      "\001(\004\032\225\003\n\004Data\022\017\n\007data_id\030\001 \001(\t\0222\n\007samples" +
      "\030\002 \003(\0132!.jlr.va.ResponseData.Data.Sample" +
      "s\0226\n\004calc\030\003 \001(\0132(.jlr.va.ResponseData.Da" +
      "ta.CalculatedData\032N\n\007Samples\022\024\n\014timestam" +
      "p_ms\030\001 \001(\004\022-\n\006sample\030\002 \001(\0132\035.jlr.va.Resp" +
      "onseData.dataType\032\277\001\n\016CalculatedData\022\023\n\013" +
      "end_time_ms\030\001 \001(\004\022\023\n\013duration_ms\030\002 \001(\r\022\013" +
      "\n\003avg\030\003 \001(\002\022*\n\003min\030\004 \001(\0132\035.jlr.va.Respon" +
      "seData.dataType\022*\n\003max\030\005 \001(\0132\035.jlr.va.Re" +
      "sponseData.dataType\022\017\n\007std_dev\030\006 \001(\002\022\r\n\005" +
      "count\030\007 \001(\rBI\n1com.jlr.vlc.protobuf.dese" +
      "rializer.protocol.raw.v3B\022RawProtobufMes" +
      "sageP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_jlr_va_ResponseData_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_jlr_va_ResponseData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_jlr_va_ResponseData_descriptor,
        new java.lang.String[] { "QueryId", "EventTimestampMs", "Data", "GlobalRT", "EmitTimestampMs", "SessionPowercycle", });
    internal_static_jlr_va_ResponseData_dataType_descriptor =
      internal_static_jlr_va_ResponseData_descriptor.getNestedTypes().get(0);
    internal_static_jlr_va_ResponseData_dataType_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_jlr_va_ResponseData_dataType_descriptor,
        new java.lang.String[] { "DataUint", "DataFloat", "DataByte", "DataString", "DataUint32", "DataBool", "DataInt32", "DataInt64", "DataDouble", "ValueOneof", });
    internal_static_jlr_va_ResponseData_GlobalRT_descriptor =
      internal_static_jlr_va_ResponseData_descriptor.getNestedTypes().get(1);
    internal_static_jlr_va_ResponseData_GlobalRT_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_jlr_va_ResponseData_GlobalRT_descriptor,
        new java.lang.String[] { "RTValue", "RTTimestampMs", });
    internal_static_jlr_va_ResponseData_Data_descriptor =
      internal_static_jlr_va_ResponseData_descriptor.getNestedTypes().get(2);
    internal_static_jlr_va_ResponseData_Data_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_jlr_va_ResponseData_Data_descriptor,
        new java.lang.String[] { "DataId", "Samples", "Calc", });
    internal_static_jlr_va_ResponseData_Data_Samples_descriptor =
      internal_static_jlr_va_ResponseData_Data_descriptor.getNestedTypes().get(0);
    internal_static_jlr_va_ResponseData_Data_Samples_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_jlr_va_ResponseData_Data_Samples_descriptor,
        new java.lang.String[] { "TimestampMs", "Sample", });
    internal_static_jlr_va_ResponseData_Data_CalculatedData_descriptor =
      internal_static_jlr_va_ResponseData_Data_descriptor.getNestedTypes().get(1);
    internal_static_jlr_va_ResponseData_Data_CalculatedData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_jlr_va_ResponseData_Data_CalculatedData_descriptor,
        new java.lang.String[] { "EndTimeMs", "DurationMs", "Avg", "Min", "Max", "StdDev", "Count", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
