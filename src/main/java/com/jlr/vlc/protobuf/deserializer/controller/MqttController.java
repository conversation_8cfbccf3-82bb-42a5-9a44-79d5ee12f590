/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.controller;

import static java.util.UUID.randomUUID;

import com.hivemq.client.mqtt.mqtt5.message.publish.Mqtt5Publish;
import com.jlr.vlc.protobuf.deserializer.config.MqttConfig;
import com.jlr.vlc.protobuf.deserializer.config.kafka.KafkaConfig;
import com.jlr.vlc.protobuf.deserializer.service.MqttService;
import com.timgroup.statsd.StatsDClient;
import datadog.trace.api.Trace;
import jakarta.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.function.Function;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.json.JSONObject;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;

/**
 * No need to write unit tests as implemented methods
 * are calling library methods and no business logic present.
 */
@Slf4j
@RequiredArgsConstructor
@Controller
public class MqttController {

  private static final String CORRELATION_ID = "correlation_id";
  private static final String LOG_CORRELATION_ID = "log_correlation_id";
  private static final String PROTOBUF_TIMESTAMP = "protobuf_timestamp";
  private final MqttService mqttService;
  private final MqttConfig mqttConfig;
  private final KafkaConfig kafkaConfig;
  private final Function<Mqtt5Publish, JSONObject> mqttMessageCallback;
  private final KafkaSender<Object, Object> kafkaSender;

  @Value("${logging.throttle.frequency:0}")
  private int throttleFrequency;
  @Value("${logging.throttle.enabled:false}")
  private Boolean throttleEnabled;
  private int counter = 0;
  private final StatsDClient statsDClient;

  @PostConstruct
  @SneakyThrows
  @Trace(operationName = "MqttController.init", resourceName = "data.deserialize")
  public void init() {
    mqttConfig.getTopics().forEach(
        (name, topic) -> mqttService.subscribe(topic, mqttConfig.getQosLevel())
            .doOnSingle(mqtt5SubAck -> log.info("Subscribed to: {}", topic))
            .parallel()
            .runOn(Schedulers.parallel())
            .doOnNext(i -> statsDClient.incrementCounter("messages.ingested.total"))
            .flatMap(this::getJsonObjectFlux)
            .map(jsonObject -> constructPayload(jsonObject, name))
            .as(this::sendToKafka)
            .subscribe(
                sentRecordMetaData -> log.debug("Sent Message to Kafka #{} result: {}",
                    sentRecordMetaData.correlationMetadata(),
                    sentRecordMetaData.recordMetadata())
            )
    );
    mqttService.connect().subscribe();
  }

  private Flux<JSONObject> getJsonObjectFlux(Mqtt5Publish message) {
    try {
      return Flux.just(mqttMessageCallback.apply(message));
    } catch (Exception exception) {
      log.error("Error processing MQTT message: {} from topic: {}", exception.getMessage(),
          message.getTopic());
      return Flux.empty();
    }
  }

  @Trace(operationName = "MqttController.sendToKafka", resourceName = "data.deserialize")
  Flux<SenderResult<String>> sendToKafka(
      Publisher<? extends SenderRecord<Object, Object, String>> records) {
    return kafkaSender.send(records);
  }

  @Trace(operationName = "MqttController.constructPayload", resourceName = "data.deserialize")
  private SenderRecord<Object, Object, String> constructPayload(JSONObject payload, String name) {
    String vehicleId = payload.getString("unique_id");
    String uuid = randomUUID().toString();
    Instant instant = Instant.now();
    String protobufTimestamp =
        instant.atZone(ZoneId.systemDefault()).format(DateTimeFormatter.ISO_DATE_TIME);
    long endTimestamp = instant.toEpochMilli();

    payload.put("end_deserialisation_timestamp", endTimestamp);

    ProducerRecord<Object, Object> producerRecord = new ProducerRecord<>(
        kafkaConfig.getOutBoundTopic(),
        vehicleId,
        payload.toString()
    );

    addUuidAndTimestampHeaders(producerRecord, uuid, protobufTimestamp);

    logCorrelationAndFlag(producerRecord, uuid, protobufTimestamp, vehicleId);
    log.debug(
        "Correlation ID {} assigned at {} for Kafka message from vehicle: {} with payload: {}",
        uuid, protobufTimestamp, vehicleId, payload);

    return SenderRecord.create(
        producerRecord,
        name
    );
  }

  private void logCorrelationAndFlag(ProducerRecord<Object, Object> producerRecord, String uuid,
                                     String protobufTimestamp, String vehicleId) {
    if (!isThrottled()) {
      Header logCorrelation = new RecordHeader(
          LOG_CORRELATION_ID, "true".getBytes(StandardCharsets.UTF_8));
      producerRecord.headers().add(logCorrelation);

      if (vehicleId.length() >= 8) {
        String vehicleIdHidden = vehicleId.substring(0, 8) + "-****-****-****-************";
        log.info("Correlation ID {} assigned at {} for Kafka message from vehicle: {}",
            uuid, protobufTimestamp, vehicleIdHidden);
      }
    }
  }

  private void addUuidAndTimestampHeaders(ProducerRecord<Object, Object> producerRecord,
                                          String uniqueID,
                                          String protobufTimestamp) {
    Header uniqueIdHeader = new RecordHeader(
        CORRELATION_ID, uniqueID.getBytes(StandardCharsets.UTF_8));
    Header timestampHeader = new RecordHeader(
        PROTOBUF_TIMESTAMP, protobufTimestamp.getBytes(StandardCharsets.UTF_8));
    producerRecord.headers().add(uniqueIdHeader);
    producerRecord.headers().add(timestampHeader);
  }

  private boolean isThrottled() {
    if (throttleEnabled == null || !throttleEnabled) {
      return false;
    }
    if (counter < throttleFrequency) {
      counter++;
      return true;
    }
    counter = 0;
    return false;
  }
}
