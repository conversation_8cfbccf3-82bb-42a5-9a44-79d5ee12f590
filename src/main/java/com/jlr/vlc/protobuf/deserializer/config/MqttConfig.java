/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.config;

import java.util.HashMap;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@ConfigurationProperties("mqtt")
public class MqttConfig {

  private final boolean automaticReconnect;

  private final boolean cleanSession;

  private final boolean retain;

  private final int connectionTimeout;

  private final int keepAliveInterval;

  private final String clientId;

  private final String protocol;

  private final String hostname;

  private final int port;

  private final int qosLevel;

  private final String groupId;

  private final HashMap<String, String> topics;
}
