/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.config.kafka;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Getter
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@ConfigurationProperties("kafka")
public class KafkaConfig {

  private final String bootStrapServers;

  private final String clientId;

  private final Long retryBackoff;

  private final Integer maxInFlightRequests;

  private final String acksConfig;

  private final String securityProtocol;

  private final String saslMechanism;

  private final String jaasConfig;

  private final String clientCallback;

  private final String outBoundTopic;
}
