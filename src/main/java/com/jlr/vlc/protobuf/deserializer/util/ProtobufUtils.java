/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.util;

import com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData;

public class ProtobufUtils {

  private static final char[] HEX_ARRAY = "0123456789ABCDEF".toCharArray();

  public static Object getOneOfValue(ResponseData.dataType dataType) {
    return switch (dataType.getValueOneofCase().toString()) {
      case "DATA_UINT" -> dataType.getDataUint();
      case "DATA_FLOAT" -> dataType.getDataFloat();
      case "DATA_BYTE" -> bytesToHex(dataType.getDataByte().toByteArray());
      case "DATA_STRING" -> dataType.getDataString();
      case "DATA_UINT32" -> dataType.getDataUint32();
      case "DATA_BOOL" -> dataType.getDataBool();
      case "DATA_INT32" -> dataType.getDataInt32();
      case "DATA_INT64" -> dataType.getDataInt64();
      case "DATA_DOUBLE" -> dataType.getDataDouble();
      default -> null;
    };
  }

  private static String bytesToHex(byte[] bytes) {
    char[] hexChars = new char[bytes.length * 2];
    for (int j = 0; j < bytes.length; j++) {
      int v = bytes[j] & 0xFF;
      hexChars[j * 2] = HEX_ARRAY[v >>> 4];
      hexChars[j * 2 + 1] = HEX_ARRAY[v & 0x0F];
    }
    return new String(hexChars);
  }
}
