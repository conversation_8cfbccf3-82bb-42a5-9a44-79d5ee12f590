/*
 * Copyright (c) Jaguar Land Rover Ltd 2022. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.client;

import com.hivemq.client.mqtt.MqttClientTransportConfig;
import com.hivemq.client.mqtt.mqtt5.Mqtt5Client;
import com.hivemq.client.mqtt.mqtt5.Mqtt5ClientBuilder;
import com.hivemq.client.mqtt.mqtt5.message.connect.Mqtt5Connect;
import com.hivemq.client.mqtt.mqtt5.reactor.Mqtt5ReactorClient;
import com.jlr.vlc.protobuf.deserializer.config.MqttConfig;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

@Slf4j
@Configuration
@Profile({"local", "test"})
public class LocalHiveMqClientBuilder {

  static MqttClientTransportConfig getTransportConfig(MqttConfig mqttConfig) {
    return MqttClientTransportConfig.builder()
        .serverHost(mqttConfig.getHostname())
        .serverPort(mqttConfig.getPort())
        .socketConnectTimeout(mqttConfig.getConnectionTimeout(), TimeUnit.SECONDS)
        .build();
  }

  @Bean
  public Mqtt5ReactorClient mqtt5Client(MqttConfig mqttConfig) {
    Mqtt5ClientBuilder clientBuilder = Mqtt5Client.builder();
    clientBuilder = clientBuilder.identifier(mqttConfig.getClientId())
        .transportConfig(getTransportConfig(mqttConfig));

    return Mqtt5ReactorClient.from(clientBuilder.build());
  }

  @Bean
  public Mqtt5Connect mqtt5Connect(MqttConfig mqttConfig) {
    return Mqtt5Connect.builder()
        .cleanStart(mqttConfig.isCleanSession())
        .noSessionExpiry()
        .keepAlive(mqttConfig.getKeepAliveInterval()).build();
  }
}
