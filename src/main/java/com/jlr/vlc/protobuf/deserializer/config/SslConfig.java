/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.config;

import lombok.AccessLevel;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;

@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
@ConfigurationProperties("ssl")
@Getter
public class SslConfig {

  private final String mqttClientCa;

  private final String mqttClientCert;

  private final String mqttClientKey;

  private final String password;
}
