/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.keycloakapiclient;

import com.jlr.vlc.protobuf.deserializer.config.KeycloakConfig;
import com.jlr.vlc.protobuf.deserializer.error.KeycloakApiClientServiceException;
import com.jlr.vlc.protobuf.deserializer.error.KeycloakApiClientServiceServerException;
import io.jsonwebtoken.Jwts;
import java.io.IOException;
import java.io.StringReader;
import java.security.PrivateKey;
import java.security.Security;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.Date;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.bouncycastle.openssl.jcajce.JceOpenSSLPKCS8DecryptorProviderBuilder;
import org.bouncycastle.operator.InputDecryptorProvider;
import org.bouncycastle.operator.OperatorCreationException;
import org.bouncycastle.pkcs.PKCS8EncryptedPrivateKeyInfo;
import org.bouncycastle.pkcs.PKCSException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@RequiredArgsConstructor
@Service
public class KeycloakApiClientServiceImpl implements KeycloakApiClientService {

  private static final String KEYCLOAK_CLIENT_ID = "client_id";
  private static final String KEYCLOAK_CLIENT_ASSERTION_TYPE = "client_assertion_type";
  private static final String KEYCLOAK_CLIENT_ASSERTION = "client_assertion";
  private static final String KEYCLOAK_GRANT_TYPE = "grant_type";

  private final WebClient webClient;

  private final KeycloakConfig keycloakConfig;

  public Mono<String> getTokenForClient() {

    MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();
    var jwt = getJwt();
    if (null == jwt) {
      return Mono.error(new KeycloakApiClientServiceException("Error generating JWT",
          HttpStatus.UNAUTHORIZED.value()));
    }

    formData.add(KEYCLOAK_CLIENT_ID, keycloakConfig.getClientID());
    formData.add(KEYCLOAK_CLIENT_ASSERTION, jwt);
    formData.add(KEYCLOAK_CLIENT_ASSERTION_TYPE, keycloakConfig.getAssertionType());
    formData.add(KEYCLOAK_GRANT_TYPE, keycloakConfig.getGrantType());

    return webClient
        .post()
        .uri(keycloakConfig.getAuthTokenUri())
        .header(HttpHeaders.CONTENT_TYPE,
            MediaType.APPLICATION_FORM_URLENCODED_VALUE)
        .body(BodyInserters.fromFormData(formData))
        .retrieve()
        .onStatus(HttpStatusCode::is4xxClientError,
            response -> Mono.error(
                new KeycloakApiClientServiceException("Keycloak API Client Error",
                    response.statusCode().value())))
        .onStatus(HttpStatusCode::is5xxServerError,
            response -> Mono.error(
                new KeycloakApiClientServiceServerException("Keycloak API Server Side Error.",
                    response.statusCode().value())))
        .bodyToMono(String.class);
  }

  protected String getJwt() {
    Instant timeNow = Instant.now();
    String audience = keycloakConfig.getAuthTokenUri();
    String clientId = keycloakConfig.getClientID();
    try {

      PrivateKey privateKey = loadKey(keycloakConfig.getPrivateKey(),
          keycloakConfig.getPrivateKeyPassword());

      return Jwts.builder()
          .setAudience(audience)
          .setIssuedAt(Date.from(timeNow))
          .setExpiration(Date.from(timeNow.plus(5L, ChronoUnit.MINUTES)))
          .setIssuer(clientId)
          .setSubject(clientId)
          .setId(UUID.randomUUID().toString())
          .signWith(privateKey)
          .compact();
    } catch (IOException
             | PKCSException
             | OperatorCreationException ex) {
      log.error("Error generating key", ex);
      return null;
    }
  }

  PrivateKey loadKey(String privateKey, String privateKeyPassword)
      throws IOException, PKCSException,
      OperatorCreationException {

    Security.addProvider(new BouncyCastleProvider());

    String keycloakPrivateKey = new String(Base64.getDecoder().decode(privateKey));

    try (PEMParser reader = new PEMParser(new StringReader(keycloakPrivateKey)); reader) {
      Object keyObject = reader.readObject();
      JcaPEMKeyConverter converter = new JcaPEMKeyConverter().setProvider("BC");

      JceOpenSSLPKCS8DecryptorProviderBuilder keyConverter =
          new JceOpenSSLPKCS8DecryptorProviderBuilder().setProvider("BC");
      InputDecryptorProvider provider = keyConverter.build(privateKeyPassword.toCharArray());

      PKCS8EncryptedPrivateKeyInfo privateKeyInfo = (PKCS8EncryptedPrivateKeyInfo) keyObject;
      PrivateKeyInfo pkInfo = privateKeyInfo.decryptPrivateKeyInfo(provider);

      return converter.getPrivateKey(pkInfo);
    }
  }

}
