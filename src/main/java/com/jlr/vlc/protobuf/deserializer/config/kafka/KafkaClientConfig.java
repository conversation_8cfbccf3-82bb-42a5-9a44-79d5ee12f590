/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.config.kafka;

import java.util.HashMap;
import java.util.Map;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

@Configuration
public class KafkaClientConfig {

  @Bean
  public KafkaSender<Object, Object> kafkaSender(KafkaConfig kafkaConfig) {

    Map<String, Object> configProperties = new HashMap<>();
    configProperties
        .put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaConfig.getBootStrapServers());
    configProperties.put(ProducerConfig.CLIENT_ID_CONFIG, kafkaConfig.getClientId());
    configProperties.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, Integer.MAX_VALUE);
    configProperties.put(ProducerConfig.ACKS_CONFIG, kafkaConfig.getAcksConfig());
    configProperties.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, Long.MAX_VALUE);
    configProperties.put(ProducerConfig.RETRIES_CONFIG, Integer.MAX_VALUE);
    configProperties.put(ProducerConfig.RETRY_BACKOFF_MS_CONFIG, kafkaConfig.getRetryBackoff());
    configProperties.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    configProperties.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
    configProperties.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION,
        kafkaConfig.getMaxInFlightRequests());

    if ("SASL_SSL".equals(kafkaConfig.getSecurityProtocol())) {
      configProperties.put("security.protocol", kafkaConfig.getSecurityProtocol());
      configProperties.put("sasl.mechanism", kafkaConfig.getSaslMechanism());
      configProperties.put("sasl.jaas.config", kafkaConfig.getJaasConfig());
      configProperties.put("sasl.client.callback.handler.class", kafkaConfig.getClientCallback());
    }

    return KafkaSender.create(SenderOptions.create(configProperties));
  }
}
