/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.service;

import com.hivemq.client.mqtt.mqtt5.message.connect.connack.Mqtt5ConnAck;
import com.hivemq.client.mqtt.mqtt5.message.publish.Mqtt5Publish;
import com.hivemq.client.mqtt.mqtt5.message.publish.Mqtt5PublishResult;
import com.hivemq.client.mqtt.mqtt5.message.subscribe.suback.Mqtt5SubAck;
import com.hivemq.client.mqtt.mqtt5.message.unsubscribe.unsuback.Mqtt5UnsubAck;
import com.hivemq.client.mqtt.mqtt5.reactor.Mqtt5ReactorClient;
import com.hivemq.client.rx.reactor.FluxWithSingle;
import reactor.core.publisher.Mono;
import reactor.core.publisher.ParallelFlux;

public interface MqttService {

  Mono<Mqtt5ConnAck> connect();

  Mono<Void> disconnect();

  boolean isConnected();

  ParallelFlux<Mqtt5PublishResult> publish(ParallelFlux<Mqtt5Publish> mqtt5PublishFlux);

  Mqtt5ReactorClient getClient();

  FluxWithSingle<Mqtt5Publish, Mqtt5SubAck> subscribe(String topic, int qos);

  Mono<Mqtt5UnsubAck> unsubscribe(String topic);
}
