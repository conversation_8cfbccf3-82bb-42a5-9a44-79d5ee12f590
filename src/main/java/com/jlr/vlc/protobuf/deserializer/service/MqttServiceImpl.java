/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.service;

import com.hivemq.client.mqtt.datatypes.MqttQos;
import com.hivemq.client.mqtt.mqtt5.message.connect.Mqtt5Connect;
import com.hivemq.client.mqtt.mqtt5.message.connect.connack.Mqtt5ConnAck;
import com.hivemq.client.mqtt.mqtt5.message.publish.Mqtt5Publish;
import com.hivemq.client.mqtt.mqtt5.message.publish.Mqtt5PublishResult;
import com.hivemq.client.mqtt.mqtt5.message.subscribe.suback.Mqtt5SubAck;
import com.hivemq.client.mqtt.mqtt5.message.unsubscribe.unsuback.Mqtt5UnsubAck;
import com.hivemq.client.mqtt.mqtt5.reactor.Mqtt5ReactorClient;
import com.hivemq.client.rx.reactor.FluxWithSingle;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.publisher.ParallelFlux;
import reactor.core.scheduler.Schedulers;

/**
 * No need to write unit tests as implemented methods
 * are calling library methods and no business logic present.
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MqttServiceImpl implements MqttService {

  private final Mqtt5ReactorClient mqttClient;

  private final Mqtt5Connect mqttConnectOptions;

  @Override
  public Mono<Mqtt5ConnAck> connect() {
    log.info("-----------CONNECTING TO HIVEMQ------------");

    if (isConnected()) {
      log.info("Deserialiser Already Connected - Skipping Connection");
      return Mono.empty();
    }

    return mqttClient.connect(mqttConnectOptions)
        .doOnSuccess(
            mqtt5ConnAck -> log.info("Connected to the broker, is Session Present: {}",
                mqtt5ConnAck.isSessionPresent())
        )
        .doOnError(throwable -> log.error("Connection Failed " + throwable.getMessage()))
        .ignoreElement();
  }

  @Override
  public Mono<Void> disconnect() {
    if (isConnected()) {
      return mqttClient.disconnect()
          .doOnSuccess(mqtt5Disconnect -> log.info("Disconnected from Broker Successfully"))
          .doOnError(throwable -> log
              .error("Failed to Disconnect from Broker with error: {}", throwable.getMessage()))
          .ignoreElement();
    }
    return Mono.empty();
  }

  @Override
  public boolean isConnected() {
    return mqttClient.getState().isConnected();
  }

  @Override
  public ParallelFlux<Mqtt5PublishResult> publish(ParallelFlux<Mqtt5Publish> mqtt5PublishFlux) {
    return mqttClient
        .publish(mqtt5PublishFlux)
        .parallel()
        .runOn(Schedulers.parallel());
  }

  @Override
  public FluxWithSingle<Mqtt5Publish, Mqtt5SubAck> subscribe(String topic, int qos) {
    return mqttClient
        .subscribePublishesWith()
        .topicFilter(topic)
        .qos(MqttQos.fromCode(qos))
        .applySubscribe();
  }

  @Override
  public Mono<Mqtt5UnsubAck> unsubscribe(String topic) {
    return mqttClient.unsubscribeWith()
        .topicFilter(topic)
        .applyUnsubscribe();
  }

  @Override
  public Mqtt5ReactorClient getClient() {
    return mqttClient;
  }

  @PreDestroy
  private void shutdown() {
    disconnect()
        .subscribe();
  }
}