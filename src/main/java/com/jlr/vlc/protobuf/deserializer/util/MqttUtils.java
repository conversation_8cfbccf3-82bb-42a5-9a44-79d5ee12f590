/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.util;

import com.hivemq.client.mqtt.mqtt5.datatypes.Mqtt5UserProperties;
import com.hivemq.client.mqtt.mqtt5.message.publish.Mqtt5Publish;

public class MqttUtils {

  private static final String USER_PROP_PUBLISH_RECEIVED_TIMESTAMP = "publish_received_timestamp";
  private static final String USER_PROP_FLEET_ID = "fleetId";


  public static String getVehicleId(Mqtt5Publish message) {
    // vehicle id always found at level2 of the topic name
    var level2 = message.getTopic().getLevels().get(1);
    // If EVA25 then will have _vcm appended -- so only need to return the first part
    return level2.split("_")[0];
  }


  public static String getFleetIdOrElse(Mqtt5Publish message, String defaultValue) {
    return getUserPropertyOrElseDefault(message.getUserProperties(), USER_PROP_FLEET_ID,
        defaultValue);
  }

  public static String getPublishReceivedTimestampOrElse(Mqtt5Publish message,
                                                         String defaultValue) {
    return getUserPropertyOrElseDefault(message.getUserProperties(),
        USER_PROP_PUBLISH_RECEIVED_TIMESTAMP, defaultValue);

  }

  private static String getUserPropertyOrElseDefault(
      Mqtt5UserProperties userProperties, String property, String defaultValue) {
    var publishReceivedTimestampUserProperty =
        userProperties.asList()
            .stream()
            .filter(prop -> prop.getName().toString().equals(property))
            .findFirst();
    if (publishReceivedTimestampUserProperty.isPresent()) {
      return publishReceivedTimestampUserProperty.get().getValue().toString();
    }
    return defaultValue;
  }

}

