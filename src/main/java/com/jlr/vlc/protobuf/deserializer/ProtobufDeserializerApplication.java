/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer;

import com.jlr.vlc.protobuf.deserializer.config.MqttConfig;
import com.jlr.vlc.protobuf.deserializer.config.SslConfig;
import com.jlr.vlc.protobuf.deserializer.config.kafka.KafkaConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

@Slf4j
@SpringBootApplication
@EnableConfigurationProperties({MqttConfig.class, SslConfig.class, KafkaConfig.class})
public class ProtobufDeserializerApplication {

  public static void main(String[] args) {
    SpringApplication.run(ProtobufDeserializerApplication.class, args);
  }

}
