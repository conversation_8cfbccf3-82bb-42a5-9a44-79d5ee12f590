// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: RawMessageType-3.2.proto
// Protobuf Java Version: 4.29.3

package com.jlr.vlc.protobuf.deserializer.protocol.raw.v3;

/**
 * Protobuf type {@code jlr.va.ResponseData}
 */
public final class ResponseData extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:jlr.va.ResponseData)
    ResponseDataOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 3,
      /* suffix= */ "",
      ResponseData.class.getName());
  }
  // Use ResponseData.newBuilder() to construct.
  private ResponseData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private ResponseData() {
    queryId_ = "";
    data_ = java.util.Collections.emptyList();
    sessionPowercycle_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.class, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Builder.class);
  }

  public interface dataTypeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:jlr.va.ResponseData.dataType)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 data_uint = 1;</code>
     * @return Whether the dataUint field is set.
     */
    boolean hasDataUint();
    /**
     * <code>uint64 data_uint = 1;</code>
     * @return The dataUint.
     */
    long getDataUint();

    /**
     * <code>float data_float = 2;</code>
     * @return Whether the dataFloat field is set.
     */
    boolean hasDataFloat();
    /**
     * <code>float data_float = 2;</code>
     * @return The dataFloat.
     */
    float getDataFloat();

    /**
     * <code>bytes data_byte = 3;</code>
     * @return Whether the dataByte field is set.
     */
    boolean hasDataByte();
    /**
     * <code>bytes data_byte = 3;</code>
     * @return The dataByte.
     */
    com.google.protobuf.ByteString getDataByte();

    /**
     * <code>string data_string = 4;</code>
     * @return Whether the dataString field is set.
     */
    boolean hasDataString();
    /**
     * <code>string data_string = 4;</code>
     * @return The dataString.
     */
    java.lang.String getDataString();
    /**
     * <code>string data_string = 4;</code>
     * @return The bytes for dataString.
     */
    com.google.protobuf.ByteString
        getDataStringBytes();

    /**
     * <code>uint32 data_uint32 = 5;</code>
     * @return Whether the dataUint32 field is set.
     */
    boolean hasDataUint32();
    /**
     * <code>uint32 data_uint32 = 5;</code>
     * @return The dataUint32.
     */
    int getDataUint32();

    /**
     * <code>bool data_bool = 6;</code>
     * @return Whether the dataBool field is set.
     */
    boolean hasDataBool();
    /**
     * <code>bool data_bool = 6;</code>
     * @return The dataBool.
     */
    boolean getDataBool();

    /**
     * <code>int32 data_int32 = 7;</code>
     * @return Whether the dataInt32 field is set.
     */
    boolean hasDataInt32();
    /**
     * <code>int32 data_int32 = 7;</code>
     * @return The dataInt32.
     */
    int getDataInt32();

    /**
     * <code>int64 data_int64 = 8;</code>
     * @return Whether the dataInt64 field is set.
     */
    boolean hasDataInt64();
    /**
     * <code>int64 data_int64 = 8;</code>
     * @return The dataInt64.
     */
    long getDataInt64();

    /**
     * <code>double data_double = 9;</code>
     * @return Whether the dataDouble field is set.
     */
    boolean hasDataDouble();
    /**
     * <code>double data_double = 9;</code>
     * @return The dataDouble.
     */
    double getDataDouble();

    com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.ValueOneofCase getValueOneofCase();
  }
  /**
   * Protobuf type {@code jlr.va.ResponseData.dataType}
   */
  public static final class dataType extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:jlr.va.ResponseData.dataType)
      dataTypeOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 3,
        /* suffix= */ "",
        dataType.class.getName());
    }
    // Use dataType.newBuilder() to construct.
    private dataType(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private dataType() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_dataType_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_dataType_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.class, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder.class);
    }

    private int valueOneofCase_ = 0;
    @SuppressWarnings("serial")
    private java.lang.Object valueOneof_;
    public enum ValueOneofCase
        implements com.google.protobuf.Internal.EnumLite,
            com.google.protobuf.AbstractMessage.InternalOneOfEnum {
      DATA_UINT(1),
      DATA_FLOAT(2),
      DATA_BYTE(3),
      DATA_STRING(4),
      DATA_UINT32(5),
      DATA_BOOL(6),
      DATA_INT32(7),
      DATA_INT64(8),
      DATA_DOUBLE(9),
      VALUEONEOF_NOT_SET(0);
      private final int value;
      private ValueOneofCase(int value) {
        this.value = value;
      }
      /**
       * @param value The number of the enum to look for.
       * @return The enum associated with the given number.
       * @deprecated Use {@link #forNumber(int)} instead.
       */
      @java.lang.Deprecated
      public static ValueOneofCase valueOf(int value) {
        return forNumber(value);
      }

      public static ValueOneofCase forNumber(int value) {
        switch (value) {
          case 1: return DATA_UINT;
          case 2: return DATA_FLOAT;
          case 3: return DATA_BYTE;
          case 4: return DATA_STRING;
          case 5: return DATA_UINT32;
          case 6: return DATA_BOOL;
          case 7: return DATA_INT32;
          case 8: return DATA_INT64;
          case 9: return DATA_DOUBLE;
          case 0: return VALUEONEOF_NOT_SET;
          default: return null;
        }
      }
      public int getNumber() {
        return this.value;
      }
    };

    public ValueOneofCase
    getValueOneofCase() {
      return ValueOneofCase.forNumber(
          valueOneofCase_);
    }

    public static final int DATA_UINT_FIELD_NUMBER = 1;
    /**
     * <code>uint64 data_uint = 1;</code>
     * @return Whether the dataUint field is set.
     */
    @java.lang.Override
    public boolean hasDataUint() {
      return valueOneofCase_ == 1;
    }
    /**
     * <code>uint64 data_uint = 1;</code>
     * @return The dataUint.
     */
    @java.lang.Override
    public long getDataUint() {
      if (valueOneofCase_ == 1) {
        return (java.lang.Long) valueOneof_;
      }
      return 0L;
    }

    public static final int DATA_FLOAT_FIELD_NUMBER = 2;
    /**
     * <code>float data_float = 2;</code>
     * @return Whether the dataFloat field is set.
     */
    @java.lang.Override
    public boolean hasDataFloat() {
      return valueOneofCase_ == 2;
    }
    /**
     * <code>float data_float = 2;</code>
     * @return The dataFloat.
     */
    @java.lang.Override
    public float getDataFloat() {
      if (valueOneofCase_ == 2) {
        return (java.lang.Float) valueOneof_;
      }
      return 0F;
    }

    public static final int DATA_BYTE_FIELD_NUMBER = 3;
    /**
     * <code>bytes data_byte = 3;</code>
     * @return Whether the dataByte field is set.
     */
    @java.lang.Override
    public boolean hasDataByte() {
      return valueOneofCase_ == 3;
    }
    /**
     * <code>bytes data_byte = 3;</code>
     * @return The dataByte.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDataByte() {
      if (valueOneofCase_ == 3) {
        return (com.google.protobuf.ByteString) valueOneof_;
      }
      return com.google.protobuf.ByteString.EMPTY;
    }

    public static final int DATA_STRING_FIELD_NUMBER = 4;
    /**
     * <code>string data_string = 4;</code>
     * @return Whether the dataString field is set.
     */
    public boolean hasDataString() {
      return valueOneofCase_ == 4;
    }
    /**
     * <code>string data_string = 4;</code>
     * @return The dataString.
     */
    public java.lang.String getDataString() {
      java.lang.Object ref = "";
      if (valueOneofCase_ == 4) {
        ref = valueOneof_;
      }
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (valueOneofCase_ == 4) {
          valueOneof_ = s;
        }
        return s;
      }
    }
    /**
     * <code>string data_string = 4;</code>
     * @return The bytes for dataString.
     */
    public com.google.protobuf.ByteString
        getDataStringBytes() {
      java.lang.Object ref = "";
      if (valueOneofCase_ == 4) {
        ref = valueOneof_;
      }
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        if (valueOneofCase_ == 4) {
          valueOneof_ = b;
        }
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int DATA_UINT32_FIELD_NUMBER = 5;
    /**
     * <code>uint32 data_uint32 = 5;</code>
     * @return Whether the dataUint32 field is set.
     */
    @java.lang.Override
    public boolean hasDataUint32() {
      return valueOneofCase_ == 5;
    }
    /**
     * <code>uint32 data_uint32 = 5;</code>
     * @return The dataUint32.
     */
    @java.lang.Override
    public int getDataUint32() {
      if (valueOneofCase_ == 5) {
        return (java.lang.Integer) valueOneof_;
      }
      return 0;
    }

    public static final int DATA_BOOL_FIELD_NUMBER = 6;
    /**
     * <code>bool data_bool = 6;</code>
     * @return Whether the dataBool field is set.
     */
    @java.lang.Override
    public boolean hasDataBool() {
      return valueOneofCase_ == 6;
    }
    /**
     * <code>bool data_bool = 6;</code>
     * @return The dataBool.
     */
    @java.lang.Override
    public boolean getDataBool() {
      if (valueOneofCase_ == 6) {
        return (java.lang.Boolean) valueOneof_;
      }
      return false;
    }

    public static final int DATA_INT32_FIELD_NUMBER = 7;
    /**
     * <code>int32 data_int32 = 7;</code>
     * @return Whether the dataInt32 field is set.
     */
    @java.lang.Override
    public boolean hasDataInt32() {
      return valueOneofCase_ == 7;
    }
    /**
     * <code>int32 data_int32 = 7;</code>
     * @return The dataInt32.
     */
    @java.lang.Override
    public int getDataInt32() {
      if (valueOneofCase_ == 7) {
        return (java.lang.Integer) valueOneof_;
      }
      return 0;
    }

    public static final int DATA_INT64_FIELD_NUMBER = 8;
    /**
     * <code>int64 data_int64 = 8;</code>
     * @return Whether the dataInt64 field is set.
     */
    @java.lang.Override
    public boolean hasDataInt64() {
      return valueOneofCase_ == 8;
    }
    /**
     * <code>int64 data_int64 = 8;</code>
     * @return The dataInt64.
     */
    @java.lang.Override
    public long getDataInt64() {
      if (valueOneofCase_ == 8) {
        return (java.lang.Long) valueOneof_;
      }
      return 0L;
    }

    public static final int DATA_DOUBLE_FIELD_NUMBER = 9;
    /**
     * <code>double data_double = 9;</code>
     * @return Whether the dataDouble field is set.
     */
    @java.lang.Override
    public boolean hasDataDouble() {
      return valueOneofCase_ == 9;
    }
    /**
     * <code>double data_double = 9;</code>
     * @return The dataDouble.
     */
    @java.lang.Override
    public double getDataDouble() {
      if (valueOneofCase_ == 9) {
        return (java.lang.Double) valueOneof_;
      }
      return 0D;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (valueOneofCase_ == 1) {
        output.writeUInt64(
            1, (long)((java.lang.Long) valueOneof_));
      }
      if (valueOneofCase_ == 2) {
        output.writeFloat(
            2, (float)((java.lang.Float) valueOneof_));
      }
      if (valueOneofCase_ == 3) {
        output.writeBytes(
            3, (com.google.protobuf.ByteString) valueOneof_);
      }
      if (valueOneofCase_ == 4) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, valueOneof_);
      }
      if (valueOneofCase_ == 5) {
        output.writeUInt32(
            5, (int)((java.lang.Integer) valueOneof_));
      }
      if (valueOneofCase_ == 6) {
        output.writeBool(
            6, (boolean)((java.lang.Boolean) valueOneof_));
      }
      if (valueOneofCase_ == 7) {
        output.writeInt32(
            7, (int)((java.lang.Integer) valueOneof_));
      }
      if (valueOneofCase_ == 8) {
        output.writeInt64(
            8, (long)((java.lang.Long) valueOneof_));
      }
      if (valueOneofCase_ == 9) {
        output.writeDouble(
            9, (double)((java.lang.Double) valueOneof_));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (valueOneofCase_ == 1) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(
              1, (long)((java.lang.Long) valueOneof_));
      }
      if (valueOneofCase_ == 2) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(
              2, (float)((java.lang.Float) valueOneof_));
      }
      if (valueOneofCase_ == 3) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(
              3, (com.google.protobuf.ByteString) valueOneof_);
      }
      if (valueOneofCase_ == 4) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, valueOneof_);
      }
      if (valueOneofCase_ == 5) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(
              5, (int)((java.lang.Integer) valueOneof_));
      }
      if (valueOneofCase_ == 6) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(
              6, (boolean)((java.lang.Boolean) valueOneof_));
      }
      if (valueOneofCase_ == 7) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(
              7, (int)((java.lang.Integer) valueOneof_));
      }
      if (valueOneofCase_ == 8) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(
              8, (long)((java.lang.Long) valueOneof_));
      }
      if (valueOneofCase_ == 9) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(
              9, (double)((java.lang.Double) valueOneof_));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType)) {
        return super.equals(obj);
      }
      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType other = (com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType) obj;

      if (!getValueOneofCase().equals(other.getValueOneofCase())) return false;
      switch (valueOneofCase_) {
        case 1:
          if (getDataUint()
              != other.getDataUint()) return false;
          break;
        case 2:
          if (java.lang.Float.floatToIntBits(getDataFloat())
              != java.lang.Float.floatToIntBits(
                  other.getDataFloat())) return false;
          break;
        case 3:
          if (!getDataByte()
              .equals(other.getDataByte())) return false;
          break;
        case 4:
          if (!getDataString()
              .equals(other.getDataString())) return false;
          break;
        case 5:
          if (getDataUint32()
              != other.getDataUint32()) return false;
          break;
        case 6:
          if (getDataBool()
              != other.getDataBool()) return false;
          break;
        case 7:
          if (getDataInt32()
              != other.getDataInt32()) return false;
          break;
        case 8:
          if (getDataInt64()
              != other.getDataInt64()) return false;
          break;
        case 9:
          if (java.lang.Double.doubleToLongBits(getDataDouble())
              != java.lang.Double.doubleToLongBits(
                  other.getDataDouble())) return false;
          break;
        case 0:
        default:
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      switch (valueOneofCase_) {
        case 1:
          hash = (37 * hash) + DATA_UINT_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getDataUint());
          break;
        case 2:
          hash = (37 * hash) + DATA_FLOAT_FIELD_NUMBER;
          hash = (53 * hash) + java.lang.Float.floatToIntBits(
              getDataFloat());
          break;
        case 3:
          hash = (37 * hash) + DATA_BYTE_FIELD_NUMBER;
          hash = (53 * hash) + getDataByte().hashCode();
          break;
        case 4:
          hash = (37 * hash) + DATA_STRING_FIELD_NUMBER;
          hash = (53 * hash) + getDataString().hashCode();
          break;
        case 5:
          hash = (37 * hash) + DATA_UINT32_FIELD_NUMBER;
          hash = (53 * hash) + getDataUint32();
          break;
        case 6:
          hash = (37 * hash) + DATA_BOOL_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
              getDataBool());
          break;
        case 7:
          hash = (37 * hash) + DATA_INT32_FIELD_NUMBER;
          hash = (53 * hash) + getDataInt32();
          break;
        case 8:
          hash = (37 * hash) + DATA_INT64_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              getDataInt64());
          break;
        case 9:
          hash = (37 * hash) + DATA_DOUBLE_FIELD_NUMBER;
          hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
              java.lang.Double.doubleToLongBits(getDataDouble()));
          break;
        case 0:
        default:
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code jlr.va.ResponseData.dataType}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:jlr.va.ResponseData.dataType)
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_dataType_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_dataType_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.class, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder.class);
      }

      // Construct using com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        valueOneofCase_ = 0;
        valueOneof_ = null;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_dataType_descriptor;
      }

      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType getDefaultInstanceForType() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance();
      }

      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType build() {
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType buildPartial() {
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType result = new com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        buildPartialOneofs(result);
        onBuilt();
        return result;
      }

      private void buildPartial0(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType result) {
        int from_bitField0_ = bitField0_;
      }

      private void buildPartialOneofs(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType result) {
        result.valueOneofCase_ = valueOneofCase_;
        result.valueOneof_ = this.valueOneof_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType) {
          return mergeFrom((com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType other) {
        if (other == com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance()) return this;
        switch (other.getValueOneofCase()) {
          case DATA_UINT: {
            setDataUint(other.getDataUint());
            break;
          }
          case DATA_FLOAT: {
            setDataFloat(other.getDataFloat());
            break;
          }
          case DATA_BYTE: {
            setDataByte(other.getDataByte());
            break;
          }
          case DATA_STRING: {
            valueOneofCase_ = 4;
            valueOneof_ = other.valueOneof_;
            onChanged();
            break;
          }
          case DATA_UINT32: {
            setDataUint32(other.getDataUint32());
            break;
          }
          case DATA_BOOL: {
            setDataBool(other.getDataBool());
            break;
          }
          case DATA_INT32: {
            setDataInt32(other.getDataInt32());
            break;
          }
          case DATA_INT64: {
            setDataInt64(other.getDataInt64());
            break;
          }
          case DATA_DOUBLE: {
            setDataDouble(other.getDataDouble());
            break;
          }
          case VALUEONEOF_NOT_SET: {
            break;
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                valueOneof_ = input.readUInt64();
                valueOneofCase_ = 1;
                break;
              } // case 8
              case 21: {
                valueOneof_ = input.readFloat();
                valueOneofCase_ = 2;
                break;
              } // case 21
              case 26: {
                valueOneof_ = input.readBytes();
                valueOneofCase_ = 3;
                break;
              } // case 26
              case 34: {
                java.lang.String s = input.readStringRequireUtf8();
                valueOneofCase_ = 4;
                valueOneof_ = s;
                break;
              } // case 34
              case 40: {
                valueOneof_ = input.readUInt32();
                valueOneofCase_ = 5;
                break;
              } // case 40
              case 48: {
                valueOneof_ = input.readBool();
                valueOneofCase_ = 6;
                break;
              } // case 48
              case 56: {
                valueOneof_ = input.readInt32();
                valueOneofCase_ = 7;
                break;
              } // case 56
              case 64: {
                valueOneof_ = input.readInt64();
                valueOneofCase_ = 8;
                break;
              } // case 64
              case 73: {
                valueOneof_ = input.readDouble();
                valueOneofCase_ = 9;
                break;
              } // case 73
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int valueOneofCase_ = 0;
      private java.lang.Object valueOneof_;
      public ValueOneofCase
          getValueOneofCase() {
        return ValueOneofCase.forNumber(
            valueOneofCase_);
      }

      public Builder clearValueOneof() {
        valueOneofCase_ = 0;
        valueOneof_ = null;
        onChanged();
        return this;
      }

      private int bitField0_;

      /**
       * <code>uint64 data_uint = 1;</code>
       * @return Whether the dataUint field is set.
       */
      public boolean hasDataUint() {
        return valueOneofCase_ == 1;
      }
      /**
       * <code>uint64 data_uint = 1;</code>
       * @return The dataUint.
       */
      public long getDataUint() {
        if (valueOneofCase_ == 1) {
          return (java.lang.Long) valueOneof_;
        }
        return 0L;
      }
      /**
       * <code>uint64 data_uint = 1;</code>
       * @param value The dataUint to set.
       * @return This builder for chaining.
       */
      public Builder setDataUint(long value) {

        valueOneofCase_ = 1;
        valueOneof_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 data_uint = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataUint() {
        if (valueOneofCase_ == 1) {
          valueOneofCase_ = 0;
          valueOneof_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>float data_float = 2;</code>
       * @return Whether the dataFloat field is set.
       */
      public boolean hasDataFloat() {
        return valueOneofCase_ == 2;
      }
      /**
       * <code>float data_float = 2;</code>
       * @return The dataFloat.
       */
      public float getDataFloat() {
        if (valueOneofCase_ == 2) {
          return (java.lang.Float) valueOneof_;
        }
        return 0F;
      }
      /**
       * <code>float data_float = 2;</code>
       * @param value The dataFloat to set.
       * @return This builder for chaining.
       */
      public Builder setDataFloat(float value) {

        valueOneofCase_ = 2;
        valueOneof_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>float data_float = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataFloat() {
        if (valueOneofCase_ == 2) {
          valueOneofCase_ = 0;
          valueOneof_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>bytes data_byte = 3;</code>
       * @return Whether the dataByte field is set.
       */
      public boolean hasDataByte() {
        return valueOneofCase_ == 3;
      }
      /**
       * <code>bytes data_byte = 3;</code>
       * @return The dataByte.
       */
      public com.google.protobuf.ByteString getDataByte() {
        if (valueOneofCase_ == 3) {
          return (com.google.protobuf.ByteString) valueOneof_;
        }
        return com.google.protobuf.ByteString.EMPTY;
      }
      /**
       * <code>bytes data_byte = 3;</code>
       * @param value The dataByte to set.
       * @return This builder for chaining.
       */
      public Builder setDataByte(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        valueOneofCase_ = 3;
        valueOneof_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bytes data_byte = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataByte() {
        if (valueOneofCase_ == 3) {
          valueOneofCase_ = 0;
          valueOneof_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>string data_string = 4;</code>
       * @return Whether the dataString field is set.
       */
      @java.lang.Override
      public boolean hasDataString() {
        return valueOneofCase_ == 4;
      }
      /**
       * <code>string data_string = 4;</code>
       * @return The dataString.
       */
      @java.lang.Override
      public java.lang.String getDataString() {
        java.lang.Object ref = "";
        if (valueOneofCase_ == 4) {
          ref = valueOneof_;
        }
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (valueOneofCase_ == 4) {
            valueOneof_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string data_string = 4;</code>
       * @return The bytes for dataString.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString
          getDataStringBytes() {
        java.lang.Object ref = "";
        if (valueOneofCase_ == 4) {
          ref = valueOneof_;
        }
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          if (valueOneofCase_ == 4) {
            valueOneof_ = b;
          }
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string data_string = 4;</code>
       * @param value The dataString to set.
       * @return This builder for chaining.
       */
      public Builder setDataString(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        valueOneofCase_ = 4;
        valueOneof_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string data_string = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataString() {
        if (valueOneofCase_ == 4) {
          valueOneofCase_ = 0;
          valueOneof_ = null;
          onChanged();
        }
        return this;
      }
      /**
       * <code>string data_string = 4;</code>
       * @param value The bytes for dataString to set.
       * @return This builder for chaining.
       */
      public Builder setDataStringBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        valueOneofCase_ = 4;
        valueOneof_ = value;
        onChanged();
        return this;
      }

      /**
       * <code>uint32 data_uint32 = 5;</code>
       * @return Whether the dataUint32 field is set.
       */
      public boolean hasDataUint32() {
        return valueOneofCase_ == 5;
      }
      /**
       * <code>uint32 data_uint32 = 5;</code>
       * @return The dataUint32.
       */
      public int getDataUint32() {
        if (valueOneofCase_ == 5) {
          return (java.lang.Integer) valueOneof_;
        }
        return 0;
      }
      /**
       * <code>uint32 data_uint32 = 5;</code>
       * @param value The dataUint32 to set.
       * @return This builder for chaining.
       */
      public Builder setDataUint32(int value) {

        valueOneofCase_ = 5;
        valueOneof_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 data_uint32 = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataUint32() {
        if (valueOneofCase_ == 5) {
          valueOneofCase_ = 0;
          valueOneof_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>bool data_bool = 6;</code>
       * @return Whether the dataBool field is set.
       */
      public boolean hasDataBool() {
        return valueOneofCase_ == 6;
      }
      /**
       * <code>bool data_bool = 6;</code>
       * @return The dataBool.
       */
      public boolean getDataBool() {
        if (valueOneofCase_ == 6) {
          return (java.lang.Boolean) valueOneof_;
        }
        return false;
      }
      /**
       * <code>bool data_bool = 6;</code>
       * @param value The dataBool to set.
       * @return This builder for chaining.
       */
      public Builder setDataBool(boolean value) {

        valueOneofCase_ = 6;
        valueOneof_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool data_bool = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataBool() {
        if (valueOneofCase_ == 6) {
          valueOneofCase_ = 0;
          valueOneof_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>int32 data_int32 = 7;</code>
       * @return Whether the dataInt32 field is set.
       */
      public boolean hasDataInt32() {
        return valueOneofCase_ == 7;
      }
      /**
       * <code>int32 data_int32 = 7;</code>
       * @return The dataInt32.
       */
      public int getDataInt32() {
        if (valueOneofCase_ == 7) {
          return (java.lang.Integer) valueOneof_;
        }
        return 0;
      }
      /**
       * <code>int32 data_int32 = 7;</code>
       * @param value The dataInt32 to set.
       * @return This builder for chaining.
       */
      public Builder setDataInt32(int value) {

        valueOneofCase_ = 7;
        valueOneof_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 data_int32 = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataInt32() {
        if (valueOneofCase_ == 7) {
          valueOneofCase_ = 0;
          valueOneof_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>int64 data_int64 = 8;</code>
       * @return Whether the dataInt64 field is set.
       */
      public boolean hasDataInt64() {
        return valueOneofCase_ == 8;
      }
      /**
       * <code>int64 data_int64 = 8;</code>
       * @return The dataInt64.
       */
      public long getDataInt64() {
        if (valueOneofCase_ == 8) {
          return (java.lang.Long) valueOneof_;
        }
        return 0L;
      }
      /**
       * <code>int64 data_int64 = 8;</code>
       * @param value The dataInt64 to set.
       * @return This builder for chaining.
       */
      public Builder setDataInt64(long value) {

        valueOneofCase_ = 8;
        valueOneof_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 data_int64 = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataInt64() {
        if (valueOneofCase_ == 8) {
          valueOneofCase_ = 0;
          valueOneof_ = null;
          onChanged();
        }
        return this;
      }

      /**
       * <code>double data_double = 9;</code>
       * @return Whether the dataDouble field is set.
       */
      public boolean hasDataDouble() {
        return valueOneofCase_ == 9;
      }
      /**
       * <code>double data_double = 9;</code>
       * @return The dataDouble.
       */
      public double getDataDouble() {
        if (valueOneofCase_ == 9) {
          return (java.lang.Double) valueOneof_;
        }
        return 0D;
      }
      /**
       * <code>double data_double = 9;</code>
       * @param value The dataDouble to set.
       * @return This builder for chaining.
       */
      public Builder setDataDouble(double value) {

        valueOneofCase_ = 9;
        valueOneof_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>double data_double = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataDouble() {
        if (valueOneofCase_ == 9) {
          valueOneofCase_ = 0;
          valueOneof_ = null;
          onChanged();
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:jlr.va.ResponseData.dataType)
    }

    // @@protoc_insertion_point(class_scope:jlr.va.ResponseData.dataType)
    private static final com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType();
    }

    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<dataType>
        PARSER = new com.google.protobuf.AbstractParser<dataType>() {
      @java.lang.Override
      public dataType parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<dataType> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<dataType> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface GlobalRTOrBuilder extends
      // @@protoc_insertion_point(interface_extends:jlr.va.ResponseData.GlobalRT)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 RT_value = 1;</code>
     * @return The rTValue.
     */
    long getRTValue();

    /**
     * <code>uint64 RT_timestamp_ms = 2;</code>
     * @return The rTTimestampMs.
     */
    long getRTTimestampMs();
  }
  /**
   * Protobuf type {@code jlr.va.ResponseData.GlobalRT}
   */
  public static final class GlobalRT extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:jlr.va.ResponseData.GlobalRT)
      GlobalRTOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 3,
        /* suffix= */ "",
        GlobalRT.class.getName());
    }
    // Use GlobalRT.newBuilder() to construct.
    private GlobalRT(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private GlobalRT() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_GlobalRT_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_GlobalRT_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.class, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.Builder.class);
    }

    public static final int RT_VALUE_FIELD_NUMBER = 1;
    private long rTValue_ = 0L;
    /**
     * <code>uint64 RT_value = 1;</code>
     * @return The rTValue.
     */
    @java.lang.Override
    public long getRTValue() {
      return rTValue_;
    }

    public static final int RT_TIMESTAMP_MS_FIELD_NUMBER = 2;
    private long rTTimestampMs_ = 0L;
    /**
     * <code>uint64 RT_timestamp_ms = 2;</code>
     * @return The rTTimestampMs.
     */
    @java.lang.Override
    public long getRTTimestampMs() {
      return rTTimestampMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (rTValue_ != 0L) {
        output.writeUInt64(1, rTValue_);
      }
      if (rTTimestampMs_ != 0L) {
        output.writeUInt64(2, rTTimestampMs_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (rTValue_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, rTValue_);
      }
      if (rTTimestampMs_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, rTTimestampMs_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT)) {
        return super.equals(obj);
      }
      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT other = (com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT) obj;

      if (getRTValue()
          != other.getRTValue()) return false;
      if (getRTTimestampMs()
          != other.getRTTimestampMs()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RT_VALUE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRTValue());
      hash = (37 * hash) + RT_TIMESTAMP_MS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRTTimestampMs());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code jlr.va.ResponseData.GlobalRT}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:jlr.va.ResponseData.GlobalRT)
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRTOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_GlobalRT_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_GlobalRT_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.class, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.Builder.class);
      }

      // Construct using com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        rTValue_ = 0L;
        rTTimestampMs_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_GlobalRT_descriptor;
      }

      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT getDefaultInstanceForType() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.getDefaultInstance();
      }

      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT build() {
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT buildPartial() {
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT result = new com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.rTValue_ = rTValue_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.rTTimestampMs_ = rTTimestampMs_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT) {
          return mergeFrom((com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT other) {
        if (other == com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.getDefaultInstance()) return this;
        if (other.getRTValue() != 0L) {
          setRTValue(other.getRTValue());
        }
        if (other.getRTTimestampMs() != 0L) {
          setRTTimestampMs(other.getRTTimestampMs());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                rTValue_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                rTTimestampMs_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long rTValue_ ;
      /**
       * <code>uint64 RT_value = 1;</code>
       * @return The rTValue.
       */
      @java.lang.Override
      public long getRTValue() {
        return rTValue_;
      }
      /**
       * <code>uint64 RT_value = 1;</code>
       * @param value The rTValue to set.
       * @return This builder for chaining.
       */
      public Builder setRTValue(long value) {

        rTValue_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 RT_value = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRTValue() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rTValue_ = 0L;
        onChanged();
        return this;
      }

      private long rTTimestampMs_ ;
      /**
       * <code>uint64 RT_timestamp_ms = 2;</code>
       * @return The rTTimestampMs.
       */
      @java.lang.Override
      public long getRTTimestampMs() {
        return rTTimestampMs_;
      }
      /**
       * <code>uint64 RT_timestamp_ms = 2;</code>
       * @param value The rTTimestampMs to set.
       * @return This builder for chaining.
       */
      public Builder setRTTimestampMs(long value) {

        rTTimestampMs_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 RT_timestamp_ms = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRTTimestampMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rTTimestampMs_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:jlr.va.ResponseData.GlobalRT)
    }

    // @@protoc_insertion_point(class_scope:jlr.va.ResponseData.GlobalRT)
    private static final com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT();
    }

    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<GlobalRT>
        PARSER = new com.google.protobuf.AbstractParser<GlobalRT>() {
      @java.lang.Override
      public GlobalRT parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<GlobalRT> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<GlobalRT> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:jlr.va.ResponseData.Data)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string data_id = 1;</code>
     * @return The dataId.
     */
    java.lang.String getDataId();
    /**
     * <code>string data_id = 1;</code>
     * @return The bytes for dataId.
     */
    com.google.protobuf.ByteString
        getDataIdBytes();

    /**
     * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
     */
    java.util.List<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples> 
        getSamplesList();
    /**
     * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
     */
    com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples getSamples(int index);
    /**
     * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
     */
    int getSamplesCount();
    /**
     * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
     */
    java.util.List<? extends com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.SamplesOrBuilder> 
        getSamplesOrBuilderList();
    /**
     * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
     */
    com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.SamplesOrBuilder getSamplesOrBuilder(
        int index);

    /**
     * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
     * @return Whether the calc field is set.
     */
    boolean hasCalc();
    /**
     * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
     * @return The calc.
     */
    com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData getCalc();
    /**
     * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
     */
    com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedDataOrBuilder getCalcOrBuilder();
  }
  /**
   * Protobuf type {@code jlr.va.ResponseData.Data}
   */
  public static final class Data extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:jlr.va.ResponseData.Data)
      DataOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 3,
        /* suffix= */ "",
        Data.class.getName());
    }
    // Use Data.newBuilder() to construct.
    private Data(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private Data() {
      dataId_ = "";
      samples_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.class, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Builder.class);
    }

    public interface SamplesOrBuilder extends
        // @@protoc_insertion_point(interface_extends:jlr.va.ResponseData.Data.Samples)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>uint64 timestamp_ms = 1;</code>
       * @return The timestampMs.
       */
      long getTimestampMs();

      /**
       * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
       * @return Whether the sample field is set.
       */
      boolean hasSample();
      /**
       * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
       * @return The sample.
       */
      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType getSample();
      /**
       * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
       */
      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder getSampleOrBuilder();
    }
    /**
     * Protobuf type {@code jlr.va.ResponseData.Data.Samples}
     */
    public static final class Samples extends
        com.google.protobuf.GeneratedMessage implements
        // @@protoc_insertion_point(message_implements:jlr.va.ResponseData.Data.Samples)
        SamplesOrBuilder {
    private static final long serialVersionUID = 0L;
      static {
        com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
          com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
          /* major= */ 4,
          /* minor= */ 29,
          /* patch= */ 3,
          /* suffix= */ "",
          Samples.class.getName());
      }
      // Use Samples.newBuilder() to construct.
      private Samples(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
      }
      private Samples() {
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_Samples_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_Samples_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.class, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.Builder.class);
      }

      private int bitField0_;
      public static final int TIMESTAMP_MS_FIELD_NUMBER = 1;
      private long timestampMs_ = 0L;
      /**
       * <code>uint64 timestamp_ms = 1;</code>
       * @return The timestampMs.
       */
      @java.lang.Override
      public long getTimestampMs() {
        return timestampMs_;
      }

      public static final int SAMPLE_FIELD_NUMBER = 2;
      private com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType sample_;
      /**
       * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
       * @return Whether the sample field is set.
       */
      @java.lang.Override
      public boolean hasSample() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
       * @return The sample.
       */
      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType getSample() {
        return sample_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance() : sample_;
      }
      /**
       * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
       */
      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder getSampleOrBuilder() {
        return sample_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance() : sample_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (timestampMs_ != 0L) {
          output.writeUInt64(1, timestampMs_);
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeMessage(2, getSample());
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (timestampMs_ != 0L) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt64Size(1, timestampMs_);
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, getSample());
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples)) {
          return super.equals(obj);
        }
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples other = (com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples) obj;

        if (getTimestampMs()
            != other.getTimestampMs()) return false;
        if (hasSample() != other.hasSample()) return false;
        if (hasSample()) {
          if (!getSample()
              .equals(other.getSample())) return false;
        }
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + TIMESTAMP_MS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTimestampMs());
        if (hasSample()) {
          hash = (37 * hash) + SAMPLE_FIELD_NUMBER;
          hash = (53 * hash) + getSample().hashCode();
        }
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code jlr.va.ResponseData.Data.Samples}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessage.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:jlr.va.ResponseData.Data.Samples)
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.SamplesOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_Samples_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_Samples_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.class, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.Builder.class);
        }

        // Construct using com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessage
                  .alwaysUseFieldBuilders) {
            getSampleFieldBuilder();
          }
        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          timestampMs_ = 0L;
          sample_ = null;
          if (sampleBuilder_ != null) {
            sampleBuilder_.dispose();
            sampleBuilder_ = null;
          }
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_Samples_descriptor;
        }

        @java.lang.Override
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples getDefaultInstanceForType() {
          return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.getDefaultInstance();
        }

        @java.lang.Override
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples build() {
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples buildPartial() {
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples result = new com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples result) {
          int from_bitField0_ = bitField0_;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.timestampMs_ = timestampMs_;
          }
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.sample_ = sampleBuilder_ == null
                ? sample_
                : sampleBuilder_.build();
            to_bitField0_ |= 0x00000001;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples) {
            return mergeFrom((com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples other) {
          if (other == com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.getDefaultInstance()) return this;
          if (other.getTimestampMs() != 0L) {
            setTimestampMs(other.getTimestampMs());
          }
          if (other.hasSample()) {
            mergeSample(other.getSample());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  timestampMs_ = input.readUInt64();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 18: {
                  input.readMessage(
                      getSampleFieldBuilder().getBuilder(),
                      extensionRegistry);
                  bitField0_ |= 0x00000002;
                  break;
                } // case 18
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private long timestampMs_ ;
        /**
         * <code>uint64 timestamp_ms = 1;</code>
         * @return The timestampMs.
         */
        @java.lang.Override
        public long getTimestampMs() {
          return timestampMs_;
        }
        /**
         * <code>uint64 timestamp_ms = 1;</code>
         * @param value The timestampMs to set.
         * @return This builder for chaining.
         */
        public Builder setTimestampMs(long value) {

          timestampMs_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>uint64 timestamp_ms = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearTimestampMs() {
          bitField0_ = (bitField0_ & ~0x00000001);
          timestampMs_ = 0L;
          onChanged();
          return this;
        }

        private com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType sample_;
        private com.google.protobuf.SingleFieldBuilder<
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder> sampleBuilder_;
        /**
         * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
         * @return Whether the sample field is set.
         */
        public boolean hasSample() {
          return ((bitField0_ & 0x00000002) != 0);
        }
        /**
         * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
         * @return The sample.
         */
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType getSample() {
          if (sampleBuilder_ == null) {
            return sample_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance() : sample_;
          } else {
            return sampleBuilder_.getMessage();
          }
        }
        /**
         * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
         */
        public Builder setSample(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType value) {
          if (sampleBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            sample_ = value;
          } else {
            sampleBuilder_.setMessage(value);
          }
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
         */
        public Builder setSample(
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder builderForValue) {
          if (sampleBuilder_ == null) {
            sample_ = builderForValue.build();
          } else {
            sampleBuilder_.setMessage(builderForValue.build());
          }
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
         */
        public Builder mergeSample(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType value) {
          if (sampleBuilder_ == null) {
            if (((bitField0_ & 0x00000002) != 0) &&
              sample_ != null &&
              sample_ != com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance()) {
              getSampleBuilder().mergeFrom(value);
            } else {
              sample_ = value;
            }
          } else {
            sampleBuilder_.mergeFrom(value);
          }
          if (sample_ != null) {
            bitField0_ |= 0x00000002;
            onChanged();
          }
          return this;
        }
        /**
         * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
         */
        public Builder clearSample() {
          bitField0_ = (bitField0_ & ~0x00000002);
          sample_ = null;
          if (sampleBuilder_ != null) {
            sampleBuilder_.dispose();
            sampleBuilder_ = null;
          }
          onChanged();
          return this;
        }
        /**
         * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
         */
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder getSampleBuilder() {
          bitField0_ |= 0x00000002;
          onChanged();
          return getSampleFieldBuilder().getBuilder();
        }
        /**
         * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
         */
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder getSampleOrBuilder() {
          if (sampleBuilder_ != null) {
            return sampleBuilder_.getMessageOrBuilder();
          } else {
            return sample_ == null ?
                com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance() : sample_;
          }
        }
        /**
         * <code>.jlr.va.ResponseData.dataType sample = 2;</code>
         */
        private com.google.protobuf.SingleFieldBuilder<
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder> 
            getSampleFieldBuilder() {
          if (sampleBuilder_ == null) {
            sampleBuilder_ = new com.google.protobuf.SingleFieldBuilder<
                com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder>(
                    getSample(),
                    getParentForChildren(),
                    isClean());
            sample_ = null;
          }
          return sampleBuilder_;
        }

        // @@protoc_insertion_point(builder_scope:jlr.va.ResponseData.Data.Samples)
      }

      // @@protoc_insertion_point(class_scope:jlr.va.ResponseData.Data.Samples)
      private static final com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples();
      }

      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<Samples>
          PARSER = new com.google.protobuf.AbstractParser<Samples>() {
        @java.lang.Override
        public Samples parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<Samples> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<Samples> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    public interface CalculatedDataOrBuilder extends
        // @@protoc_insertion_point(interface_extends:jlr.va.ResponseData.Data.CalculatedData)
        com.google.protobuf.MessageOrBuilder {

      /**
       * <code>uint64 end_time_ms = 1;</code>
       * @return The endTimeMs.
       */
      long getEndTimeMs();

      /**
       * <code>uint32 duration_ms = 2;</code>
       * @return The durationMs.
       */
      int getDurationMs();

      /**
       * <code>float avg = 3;</code>
       * @return The avg.
       */
      float getAvg();

      /**
       * <code>.jlr.va.ResponseData.dataType min = 4;</code>
       * @return Whether the min field is set.
       */
      boolean hasMin();
      /**
       * <code>.jlr.va.ResponseData.dataType min = 4;</code>
       * @return The min.
       */
      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType getMin();
      /**
       * <code>.jlr.va.ResponseData.dataType min = 4;</code>
       */
      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder getMinOrBuilder();

      /**
       * <code>.jlr.va.ResponseData.dataType max = 5;</code>
       * @return Whether the max field is set.
       */
      boolean hasMax();
      /**
       * <code>.jlr.va.ResponseData.dataType max = 5;</code>
       * @return The max.
       */
      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType getMax();
      /**
       * <code>.jlr.va.ResponseData.dataType max = 5;</code>
       */
      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder getMaxOrBuilder();

      /**
       * <code>float std_dev = 6;</code>
       * @return The stdDev.
       */
      float getStdDev();

      /**
       * <code>uint32 count = 7;</code>
       * @return The count.
       */
      int getCount();
    }
    /**
     * Protobuf type {@code jlr.va.ResponseData.Data.CalculatedData}
     */
    public static final class CalculatedData extends
        com.google.protobuf.GeneratedMessage implements
        // @@protoc_insertion_point(message_implements:jlr.va.ResponseData.Data.CalculatedData)
        CalculatedDataOrBuilder {
    private static final long serialVersionUID = 0L;
      static {
        com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
          com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
          /* major= */ 4,
          /* minor= */ 29,
          /* patch= */ 3,
          /* suffix= */ "",
          CalculatedData.class.getName());
      }
      // Use CalculatedData.newBuilder() to construct.
      private CalculatedData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
      }
      private CalculatedData() {
      }

      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_CalculatedData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_CalculatedData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.class, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.Builder.class);
      }

      private int bitField0_;
      public static final int END_TIME_MS_FIELD_NUMBER = 1;
      private long endTimeMs_ = 0L;
      /**
       * <code>uint64 end_time_ms = 1;</code>
       * @return The endTimeMs.
       */
      @java.lang.Override
      public long getEndTimeMs() {
        return endTimeMs_;
      }

      public static final int DURATION_MS_FIELD_NUMBER = 2;
      private int durationMs_ = 0;
      /**
       * <code>uint32 duration_ms = 2;</code>
       * @return The durationMs.
       */
      @java.lang.Override
      public int getDurationMs() {
        return durationMs_;
      }

      public static final int AVG_FIELD_NUMBER = 3;
      private float avg_ = 0F;
      /**
       * <code>float avg = 3;</code>
       * @return The avg.
       */
      @java.lang.Override
      public float getAvg() {
        return avg_;
      }

      public static final int MIN_FIELD_NUMBER = 4;
      private com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType min_;
      /**
       * <code>.jlr.va.ResponseData.dataType min = 4;</code>
       * @return Whether the min field is set.
       */
      @java.lang.Override
      public boolean hasMin() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.jlr.va.ResponseData.dataType min = 4;</code>
       * @return The min.
       */
      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType getMin() {
        return min_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance() : min_;
      }
      /**
       * <code>.jlr.va.ResponseData.dataType min = 4;</code>
       */
      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder getMinOrBuilder() {
        return min_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance() : min_;
      }

      public static final int MAX_FIELD_NUMBER = 5;
      private com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType max_;
      /**
       * <code>.jlr.va.ResponseData.dataType max = 5;</code>
       * @return Whether the max field is set.
       */
      @java.lang.Override
      public boolean hasMax() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.jlr.va.ResponseData.dataType max = 5;</code>
       * @return The max.
       */
      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType getMax() {
        return max_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance() : max_;
      }
      /**
       * <code>.jlr.va.ResponseData.dataType max = 5;</code>
       */
      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder getMaxOrBuilder() {
        return max_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance() : max_;
      }

      public static final int STD_DEV_FIELD_NUMBER = 6;
      private float stdDev_ = 0F;
      /**
       * <code>float std_dev = 6;</code>
       * @return The stdDev.
       */
      @java.lang.Override
      public float getStdDev() {
        return stdDev_;
      }

      public static final int COUNT_FIELD_NUMBER = 7;
      private int count_ = 0;
      /**
       * <code>uint32 count = 7;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }

      private byte memoizedIsInitialized = -1;
      @java.lang.Override
      public final boolean isInitialized() {
        byte isInitialized = memoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        memoizedIsInitialized = 1;
        return true;
      }

      @java.lang.Override
      public void writeTo(com.google.protobuf.CodedOutputStream output)
                          throws java.io.IOException {
        if (endTimeMs_ != 0L) {
          output.writeUInt64(1, endTimeMs_);
        }
        if (durationMs_ != 0) {
          output.writeUInt32(2, durationMs_);
        }
        if (java.lang.Float.floatToRawIntBits(avg_) != 0) {
          output.writeFloat(3, avg_);
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          output.writeMessage(4, getMin());
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          output.writeMessage(5, getMax());
        }
        if (java.lang.Float.floatToRawIntBits(stdDev_) != 0) {
          output.writeFloat(6, stdDev_);
        }
        if (count_ != 0) {
          output.writeUInt32(7, count_);
        }
        getUnknownFields().writeTo(output);
      }

      @java.lang.Override
      public int getSerializedSize() {
        int size = memoizedSize;
        if (size != -1) return size;

        size = 0;
        if (endTimeMs_ != 0L) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt64Size(1, endTimeMs_);
        }
        if (durationMs_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(2, durationMs_);
        }
        if (java.lang.Float.floatToRawIntBits(avg_) != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeFloatSize(3, avg_);
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(4, getMin());
        }
        if (((bitField0_ & 0x00000002) != 0)) {
          size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(5, getMax());
        }
        if (java.lang.Float.floatToRawIntBits(stdDev_) != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeFloatSize(6, stdDev_);
        }
        if (count_ != 0) {
          size += com.google.protobuf.CodedOutputStream
            .computeUInt32Size(7, count_);
        }
        size += getUnknownFields().getSerializedSize();
        memoizedSize = size;
        return size;
      }

      @java.lang.Override
      public boolean equals(final java.lang.Object obj) {
        if (obj == this) {
         return true;
        }
        if (!(obj instanceof com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData)) {
          return super.equals(obj);
        }
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData other = (com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData) obj;

        if (getEndTimeMs()
            != other.getEndTimeMs()) return false;
        if (getDurationMs()
            != other.getDurationMs()) return false;
        if (java.lang.Float.floatToIntBits(getAvg())
            != java.lang.Float.floatToIntBits(
                other.getAvg())) return false;
        if (hasMin() != other.hasMin()) return false;
        if (hasMin()) {
          if (!getMin()
              .equals(other.getMin())) return false;
        }
        if (hasMax() != other.hasMax()) return false;
        if (hasMax()) {
          if (!getMax()
              .equals(other.getMax())) return false;
        }
        if (java.lang.Float.floatToIntBits(getStdDev())
            != java.lang.Float.floatToIntBits(
                other.getStdDev())) return false;
        if (getCount()
            != other.getCount()) return false;
        if (!getUnknownFields().equals(other.getUnknownFields())) return false;
        return true;
      }

      @java.lang.Override
      public int hashCode() {
        if (memoizedHashCode != 0) {
          return memoizedHashCode;
        }
        int hash = 41;
        hash = (19 * hash) + getDescriptor().hashCode();
        hash = (37 * hash) + END_TIME_MS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEndTimeMs());
        hash = (37 * hash) + DURATION_MS_FIELD_NUMBER;
        hash = (53 * hash) + getDurationMs();
        hash = (37 * hash) + AVG_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getAvg());
        if (hasMin()) {
          hash = (37 * hash) + MIN_FIELD_NUMBER;
          hash = (53 * hash) + getMin().hashCode();
        }
        if (hasMax()) {
          hash = (37 * hash) + MAX_FIELD_NUMBER;
          hash = (53 * hash) + getMax().hashCode();
        }
        hash = (37 * hash) + STD_DEV_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getStdDev());
        hash = (37 * hash) + COUNT_FIELD_NUMBER;
        hash = (53 * hash) + getCount();
        hash = (29 * hash) + getUnknownFields().hashCode();
        memoizedHashCode = hash;
        return hash;
      }

      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData parseFrom(
          java.nio.ByteBuffer data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData parseFrom(
          java.nio.ByteBuffer data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData parseFrom(
          com.google.protobuf.ByteString data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData parseFrom(
          com.google.protobuf.ByteString data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData parseFrom(byte[] data)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData parseFrom(
          byte[] data,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData parseFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData parseFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData parseDelimitedFrom(java.io.InputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input);
      }

      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData parseDelimitedFrom(
          java.io.InputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData parseFrom(
          com.google.protobuf.CodedInputStream input)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input);
      }
      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData parseFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage
            .parseWithIOException(PARSER, input, extensionRegistry);
      }

      @java.lang.Override
      public Builder newBuilderForType() { return newBuilder(); }
      public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
      }
      public static Builder newBuilder(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
      }
      @java.lang.Override
      public Builder toBuilder() {
        return this == DEFAULT_INSTANCE
            ? new Builder() : new Builder().mergeFrom(this);
      }

      @java.lang.Override
      protected Builder newBuilderForType(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        Builder builder = new Builder(parent);
        return builder;
      }
      /**
       * Protobuf type {@code jlr.va.ResponseData.Data.CalculatedData}
       */
      public static final class Builder extends
          com.google.protobuf.GeneratedMessage.Builder<Builder> implements
          // @@protoc_insertion_point(builder_implements:jlr.va.ResponseData.Data.CalculatedData)
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedDataOrBuilder {
        public static final com.google.protobuf.Descriptors.Descriptor
            getDescriptor() {
          return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_CalculatedData_descriptor;
        }

        @java.lang.Override
        protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
            internalGetFieldAccessorTable() {
          return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_CalculatedData_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                  com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.class, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.Builder.class);
        }

        // Construct using com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.newBuilder()
        private Builder() {
          maybeForceBuilderInitialization();
        }

        private Builder(
            com.google.protobuf.GeneratedMessage.BuilderParent parent) {
          super(parent);
          maybeForceBuilderInitialization();
        }
        private void maybeForceBuilderInitialization() {
          if (com.google.protobuf.GeneratedMessage
                  .alwaysUseFieldBuilders) {
            getMinFieldBuilder();
            getMaxFieldBuilder();
          }
        }
        @java.lang.Override
        public Builder clear() {
          super.clear();
          bitField0_ = 0;
          endTimeMs_ = 0L;
          durationMs_ = 0;
          avg_ = 0F;
          min_ = null;
          if (minBuilder_ != null) {
            minBuilder_.dispose();
            minBuilder_ = null;
          }
          max_ = null;
          if (maxBuilder_ != null) {
            maxBuilder_.dispose();
            maxBuilder_ = null;
          }
          stdDev_ = 0F;
          count_ = 0;
          return this;
        }

        @java.lang.Override
        public com.google.protobuf.Descriptors.Descriptor
            getDescriptorForType() {
          return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_CalculatedData_descriptor;
        }

        @java.lang.Override
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData getDefaultInstanceForType() {
          return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.getDefaultInstance();
        }

        @java.lang.Override
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData build() {
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData result = buildPartial();
          if (!result.isInitialized()) {
            throw newUninitializedMessageException(result);
          }
          return result;
        }

        @java.lang.Override
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData buildPartial() {
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData result = new com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData(this);
          if (bitField0_ != 0) { buildPartial0(result); }
          onBuilt();
          return result;
        }

        private void buildPartial0(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData result) {
          int from_bitField0_ = bitField0_;
          if (((from_bitField0_ & 0x00000001) != 0)) {
            result.endTimeMs_ = endTimeMs_;
          }
          if (((from_bitField0_ & 0x00000002) != 0)) {
            result.durationMs_ = durationMs_;
          }
          if (((from_bitField0_ & 0x00000004) != 0)) {
            result.avg_ = avg_;
          }
          int to_bitField0_ = 0;
          if (((from_bitField0_ & 0x00000008) != 0)) {
            result.min_ = minBuilder_ == null
                ? min_
                : minBuilder_.build();
            to_bitField0_ |= 0x00000001;
          }
          if (((from_bitField0_ & 0x00000010) != 0)) {
            result.max_ = maxBuilder_ == null
                ? max_
                : maxBuilder_.build();
            to_bitField0_ |= 0x00000002;
          }
          if (((from_bitField0_ & 0x00000020) != 0)) {
            result.stdDev_ = stdDev_;
          }
          if (((from_bitField0_ & 0x00000040) != 0)) {
            result.count_ = count_;
          }
          result.bitField0_ |= to_bitField0_;
        }

        @java.lang.Override
        public Builder mergeFrom(com.google.protobuf.Message other) {
          if (other instanceof com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData) {
            return mergeFrom((com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData)other);
          } else {
            super.mergeFrom(other);
            return this;
          }
        }

        public Builder mergeFrom(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData other) {
          if (other == com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.getDefaultInstance()) return this;
          if (other.getEndTimeMs() != 0L) {
            setEndTimeMs(other.getEndTimeMs());
          }
          if (other.getDurationMs() != 0) {
            setDurationMs(other.getDurationMs());
          }
          if (other.getAvg() != 0F) {
            setAvg(other.getAvg());
          }
          if (other.hasMin()) {
            mergeMin(other.getMin());
          }
          if (other.hasMax()) {
            mergeMax(other.getMax());
          }
          if (other.getStdDev() != 0F) {
            setStdDev(other.getStdDev());
          }
          if (other.getCount() != 0) {
            setCount(other.getCount());
          }
          this.mergeUnknownFields(other.getUnknownFields());
          onChanged();
          return this;
        }

        @java.lang.Override
        public final boolean isInitialized() {
          return true;
        }

        @java.lang.Override
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
          if (extensionRegistry == null) {
            throw new java.lang.NullPointerException();
          }
          try {
            boolean done = false;
            while (!done) {
              int tag = input.readTag();
              switch (tag) {
                case 0:
                  done = true;
                  break;
                case 8: {
                  endTimeMs_ = input.readUInt64();
                  bitField0_ |= 0x00000001;
                  break;
                } // case 8
                case 16: {
                  durationMs_ = input.readUInt32();
                  bitField0_ |= 0x00000002;
                  break;
                } // case 16
                case 29: {
                  avg_ = input.readFloat();
                  bitField0_ |= 0x00000004;
                  break;
                } // case 29
                case 34: {
                  input.readMessage(
                      getMinFieldBuilder().getBuilder(),
                      extensionRegistry);
                  bitField0_ |= 0x00000008;
                  break;
                } // case 34
                case 42: {
                  input.readMessage(
                      getMaxFieldBuilder().getBuilder(),
                      extensionRegistry);
                  bitField0_ |= 0x00000010;
                  break;
                } // case 42
                case 53: {
                  stdDev_ = input.readFloat();
                  bitField0_ |= 0x00000020;
                  break;
                } // case 53
                case 56: {
                  count_ = input.readUInt32();
                  bitField0_ |= 0x00000040;
                  break;
                } // case 56
                default: {
                  if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                    done = true; // was an endgroup tag
                  }
                  break;
                } // default:
              } // switch (tag)
            } // while (!done)
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.unwrapIOException();
          } finally {
            onChanged();
          } // finally
          return this;
        }
        private int bitField0_;

        private long endTimeMs_ ;
        /**
         * <code>uint64 end_time_ms = 1;</code>
         * @return The endTimeMs.
         */
        @java.lang.Override
        public long getEndTimeMs() {
          return endTimeMs_;
        }
        /**
         * <code>uint64 end_time_ms = 1;</code>
         * @param value The endTimeMs to set.
         * @return This builder for chaining.
         */
        public Builder setEndTimeMs(long value) {

          endTimeMs_ = value;
          bitField0_ |= 0x00000001;
          onChanged();
          return this;
        }
        /**
         * <code>uint64 end_time_ms = 1;</code>
         * @return This builder for chaining.
         */
        public Builder clearEndTimeMs() {
          bitField0_ = (bitField0_ & ~0x00000001);
          endTimeMs_ = 0L;
          onChanged();
          return this;
        }

        private int durationMs_ ;
        /**
         * <code>uint32 duration_ms = 2;</code>
         * @return The durationMs.
         */
        @java.lang.Override
        public int getDurationMs() {
          return durationMs_;
        }
        /**
         * <code>uint32 duration_ms = 2;</code>
         * @param value The durationMs to set.
         * @return This builder for chaining.
         */
        public Builder setDurationMs(int value) {

          durationMs_ = value;
          bitField0_ |= 0x00000002;
          onChanged();
          return this;
        }
        /**
         * <code>uint32 duration_ms = 2;</code>
         * @return This builder for chaining.
         */
        public Builder clearDurationMs() {
          bitField0_ = (bitField0_ & ~0x00000002);
          durationMs_ = 0;
          onChanged();
          return this;
        }

        private float avg_ ;
        /**
         * <code>float avg = 3;</code>
         * @return The avg.
         */
        @java.lang.Override
        public float getAvg() {
          return avg_;
        }
        /**
         * <code>float avg = 3;</code>
         * @param value The avg to set.
         * @return This builder for chaining.
         */
        public Builder setAvg(float value) {

          avg_ = value;
          bitField0_ |= 0x00000004;
          onChanged();
          return this;
        }
        /**
         * <code>float avg = 3;</code>
         * @return This builder for chaining.
         */
        public Builder clearAvg() {
          bitField0_ = (bitField0_ & ~0x00000004);
          avg_ = 0F;
          onChanged();
          return this;
        }

        private com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType min_;
        private com.google.protobuf.SingleFieldBuilder<
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder> minBuilder_;
        /**
         * <code>.jlr.va.ResponseData.dataType min = 4;</code>
         * @return Whether the min field is set.
         */
        public boolean hasMin() {
          return ((bitField0_ & 0x00000008) != 0);
        }
        /**
         * <code>.jlr.va.ResponseData.dataType min = 4;</code>
         * @return The min.
         */
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType getMin() {
          if (minBuilder_ == null) {
            return min_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance() : min_;
          } else {
            return minBuilder_.getMessage();
          }
        }
        /**
         * <code>.jlr.va.ResponseData.dataType min = 4;</code>
         */
        public Builder setMin(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType value) {
          if (minBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            min_ = value;
          } else {
            minBuilder_.setMessage(value);
          }
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <code>.jlr.va.ResponseData.dataType min = 4;</code>
         */
        public Builder setMin(
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder builderForValue) {
          if (minBuilder_ == null) {
            min_ = builderForValue.build();
          } else {
            minBuilder_.setMessage(builderForValue.build());
          }
          bitField0_ |= 0x00000008;
          onChanged();
          return this;
        }
        /**
         * <code>.jlr.va.ResponseData.dataType min = 4;</code>
         */
        public Builder mergeMin(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType value) {
          if (minBuilder_ == null) {
            if (((bitField0_ & 0x00000008) != 0) &&
              min_ != null &&
              min_ != com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance()) {
              getMinBuilder().mergeFrom(value);
            } else {
              min_ = value;
            }
          } else {
            minBuilder_.mergeFrom(value);
          }
          if (min_ != null) {
            bitField0_ |= 0x00000008;
            onChanged();
          }
          return this;
        }
        /**
         * <code>.jlr.va.ResponseData.dataType min = 4;</code>
         */
        public Builder clearMin() {
          bitField0_ = (bitField0_ & ~0x00000008);
          min_ = null;
          if (minBuilder_ != null) {
            minBuilder_.dispose();
            minBuilder_ = null;
          }
          onChanged();
          return this;
        }
        /**
         * <code>.jlr.va.ResponseData.dataType min = 4;</code>
         */
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder getMinBuilder() {
          bitField0_ |= 0x00000008;
          onChanged();
          return getMinFieldBuilder().getBuilder();
        }
        /**
         * <code>.jlr.va.ResponseData.dataType min = 4;</code>
         */
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder getMinOrBuilder() {
          if (minBuilder_ != null) {
            return minBuilder_.getMessageOrBuilder();
          } else {
            return min_ == null ?
                com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance() : min_;
          }
        }
        /**
         * <code>.jlr.va.ResponseData.dataType min = 4;</code>
         */
        private com.google.protobuf.SingleFieldBuilder<
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder> 
            getMinFieldBuilder() {
          if (minBuilder_ == null) {
            minBuilder_ = new com.google.protobuf.SingleFieldBuilder<
                com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder>(
                    getMin(),
                    getParentForChildren(),
                    isClean());
            min_ = null;
          }
          return minBuilder_;
        }

        private com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType max_;
        private com.google.protobuf.SingleFieldBuilder<
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder> maxBuilder_;
        /**
         * <code>.jlr.va.ResponseData.dataType max = 5;</code>
         * @return Whether the max field is set.
         */
        public boolean hasMax() {
          return ((bitField0_ & 0x00000010) != 0);
        }
        /**
         * <code>.jlr.va.ResponseData.dataType max = 5;</code>
         * @return The max.
         */
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType getMax() {
          if (maxBuilder_ == null) {
            return max_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance() : max_;
          } else {
            return maxBuilder_.getMessage();
          }
        }
        /**
         * <code>.jlr.va.ResponseData.dataType max = 5;</code>
         */
        public Builder setMax(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType value) {
          if (maxBuilder_ == null) {
            if (value == null) {
              throw new NullPointerException();
            }
            max_ = value;
          } else {
            maxBuilder_.setMessage(value);
          }
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <code>.jlr.va.ResponseData.dataType max = 5;</code>
         */
        public Builder setMax(
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder builderForValue) {
          if (maxBuilder_ == null) {
            max_ = builderForValue.build();
          } else {
            maxBuilder_.setMessage(builderForValue.build());
          }
          bitField0_ |= 0x00000010;
          onChanged();
          return this;
        }
        /**
         * <code>.jlr.va.ResponseData.dataType max = 5;</code>
         */
        public Builder mergeMax(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType value) {
          if (maxBuilder_ == null) {
            if (((bitField0_ & 0x00000010) != 0) &&
              max_ != null &&
              max_ != com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance()) {
              getMaxBuilder().mergeFrom(value);
            } else {
              max_ = value;
            }
          } else {
            maxBuilder_.mergeFrom(value);
          }
          if (max_ != null) {
            bitField0_ |= 0x00000010;
            onChanged();
          }
          return this;
        }
        /**
         * <code>.jlr.va.ResponseData.dataType max = 5;</code>
         */
        public Builder clearMax() {
          bitField0_ = (bitField0_ & ~0x00000010);
          max_ = null;
          if (maxBuilder_ != null) {
            maxBuilder_.dispose();
            maxBuilder_ = null;
          }
          onChanged();
          return this;
        }
        /**
         * <code>.jlr.va.ResponseData.dataType max = 5;</code>
         */
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder getMaxBuilder() {
          bitField0_ |= 0x00000010;
          onChanged();
          return getMaxFieldBuilder().getBuilder();
        }
        /**
         * <code>.jlr.va.ResponseData.dataType max = 5;</code>
         */
        public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder getMaxOrBuilder() {
          if (maxBuilder_ != null) {
            return maxBuilder_.getMessageOrBuilder();
          } else {
            return max_ == null ?
                com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.getDefaultInstance() : max_;
          }
        }
        /**
         * <code>.jlr.va.ResponseData.dataType max = 5;</code>
         */
        private com.google.protobuf.SingleFieldBuilder<
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder> 
            getMaxFieldBuilder() {
          if (maxBuilder_ == null) {
            maxBuilder_ = new com.google.protobuf.SingleFieldBuilder<
                com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataType.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.dataTypeOrBuilder>(
                    getMax(),
                    getParentForChildren(),
                    isClean());
            max_ = null;
          }
          return maxBuilder_;
        }

        private float stdDev_ ;
        /**
         * <code>float std_dev = 6;</code>
         * @return The stdDev.
         */
        @java.lang.Override
        public float getStdDev() {
          return stdDev_;
        }
        /**
         * <code>float std_dev = 6;</code>
         * @param value The stdDev to set.
         * @return This builder for chaining.
         */
        public Builder setStdDev(float value) {

          stdDev_ = value;
          bitField0_ |= 0x00000020;
          onChanged();
          return this;
        }
        /**
         * <code>float std_dev = 6;</code>
         * @return This builder for chaining.
         */
        public Builder clearStdDev() {
          bitField0_ = (bitField0_ & ~0x00000020);
          stdDev_ = 0F;
          onChanged();
          return this;
        }

        private int count_ ;
        /**
         * <code>uint32 count = 7;</code>
         * @return The count.
         */
        @java.lang.Override
        public int getCount() {
          return count_;
        }
        /**
         * <code>uint32 count = 7;</code>
         * @param value The count to set.
         * @return This builder for chaining.
         */
        public Builder setCount(int value) {

          count_ = value;
          bitField0_ |= 0x00000040;
          onChanged();
          return this;
        }
        /**
         * <code>uint32 count = 7;</code>
         * @return This builder for chaining.
         */
        public Builder clearCount() {
          bitField0_ = (bitField0_ & ~0x00000040);
          count_ = 0;
          onChanged();
          return this;
        }

        // @@protoc_insertion_point(builder_scope:jlr.va.ResponseData.Data.CalculatedData)
      }

      // @@protoc_insertion_point(class_scope:jlr.va.ResponseData.Data.CalculatedData)
      private static final com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData DEFAULT_INSTANCE;
      static {
        DEFAULT_INSTANCE = new com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData();
      }

      public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData getDefaultInstance() {
        return DEFAULT_INSTANCE;
      }

      private static final com.google.protobuf.Parser<CalculatedData>
          PARSER = new com.google.protobuf.AbstractParser<CalculatedData>() {
        @java.lang.Override
        public CalculatedData parsePartialFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
          Builder builder = newBuilder();
          try {
            builder.mergeFrom(input, extensionRegistry);
          } catch (com.google.protobuf.InvalidProtocolBufferException e) {
            throw e.setUnfinishedMessage(builder.buildPartial());
          } catch (com.google.protobuf.UninitializedMessageException e) {
            throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
          } catch (java.io.IOException e) {
            throw new com.google.protobuf.InvalidProtocolBufferException(e)
                .setUnfinishedMessage(builder.buildPartial());
          }
          return builder.buildPartial();
        }
      };

      public static com.google.protobuf.Parser<CalculatedData> parser() {
        return PARSER;
      }

      @java.lang.Override
      public com.google.protobuf.Parser<CalculatedData> getParserForType() {
        return PARSER;
      }

      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
      }

    }

    private int bitField0_;
    public static final int DATA_ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object dataId_ = "";
    /**
     * <code>string data_id = 1;</code>
     * @return The dataId.
     */
    @java.lang.Override
    public java.lang.String getDataId() {
      java.lang.Object ref = dataId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        dataId_ = s;
        return s;
      }
    }
    /**
     * <code>string data_id = 1;</code>
     * @return The bytes for dataId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDataIdBytes() {
      java.lang.Object ref = dataId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        dataId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SAMPLES_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples> samples_;
    /**
     * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples> getSamplesList() {
      return samples_;
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.SamplesOrBuilder> 
        getSamplesOrBuilderList() {
      return samples_;
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
     */
    @java.lang.Override
    public int getSamplesCount() {
      return samples_.size();
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
     */
    @java.lang.Override
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples getSamples(int index) {
      return samples_.get(index);
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
     */
    @java.lang.Override
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.SamplesOrBuilder getSamplesOrBuilder(
        int index) {
      return samples_.get(index);
    }

    public static final int CALC_FIELD_NUMBER = 3;
    private com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData calc_;
    /**
     * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
     * @return Whether the calc field is set.
     */
    @java.lang.Override
    public boolean hasCalc() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
     * @return The calc.
     */
    @java.lang.Override
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData getCalc() {
      return calc_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.getDefaultInstance() : calc_;
    }
    /**
     * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
     */
    @java.lang.Override
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedDataOrBuilder getCalcOrBuilder() {
      return calc_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.getDefaultInstance() : calc_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(dataId_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, dataId_);
      }
      for (int i = 0; i < samples_.size(); i++) {
        output.writeMessage(2, samples_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(3, getCalc());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(dataId_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, dataId_);
      }
      for (int i = 0; i < samples_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, samples_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCalc());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data)) {
        return super.equals(obj);
      }
      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data other = (com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data) obj;

      if (!getDataId()
          .equals(other.getDataId())) return false;
      if (!getSamplesList()
          .equals(other.getSamplesList())) return false;
      if (hasCalc() != other.hasCalc()) return false;
      if (hasCalc()) {
        if (!getCalc()
            .equals(other.getCalc())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + DATA_ID_FIELD_NUMBER;
      hash = (53 * hash) + getDataId().hashCode();
      if (getSamplesCount() > 0) {
        hash = (37 * hash) + SAMPLES_FIELD_NUMBER;
        hash = (53 * hash) + getSamplesList().hashCode();
      }
      if (hasCalc()) {
        hash = (37 * hash) + CALC_FIELD_NUMBER;
        hash = (53 * hash) + getCalc().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code jlr.va.ResponseData.Data}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:jlr.va.ResponseData.Data)
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.DataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.class, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Builder.class);
      }

      // Construct using com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getSamplesFieldBuilder();
          getCalcFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        dataId_ = "";
        if (samplesBuilder_ == null) {
          samples_ = java.util.Collections.emptyList();
        } else {
          samples_ = null;
          samplesBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        calc_ = null;
        if (calcBuilder_ != null) {
          calcBuilder_.dispose();
          calcBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_Data_descriptor;
      }

      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data getDefaultInstanceForType() {
        return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.getDefaultInstance();
      }

      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data build() {
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data buildPartial() {
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data result = new com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data result) {
        if (samplesBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            samples_ = java.util.Collections.unmodifiableList(samples_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.samples_ = samples_;
        } else {
          result.samples_ = samplesBuilder_.build();
        }
      }

      private void buildPartial0(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.dataId_ = dataId_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.calc_ = calcBuilder_ == null
              ? calc_
              : calcBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data) {
          return mergeFrom((com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data other) {
        if (other == com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.getDefaultInstance()) return this;
        if (!other.getDataId().isEmpty()) {
          dataId_ = other.dataId_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (samplesBuilder_ == null) {
          if (!other.samples_.isEmpty()) {
            if (samples_.isEmpty()) {
              samples_ = other.samples_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureSamplesIsMutable();
              samples_.addAll(other.samples_);
            }
            onChanged();
          }
        } else {
          if (!other.samples_.isEmpty()) {
            if (samplesBuilder_.isEmpty()) {
              samplesBuilder_.dispose();
              samplesBuilder_ = null;
              samples_ = other.samples_;
              bitField0_ = (bitField0_ & ~0x00000002);
              samplesBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getSamplesFieldBuilder() : null;
            } else {
              samplesBuilder_.addAllMessages(other.samples_);
            }
          }
        }
        if (other.hasCalc()) {
          mergeCalc(other.getCalc());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                dataId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples m =
                    input.readMessage(
                        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.parser(),
                        extensionRegistry);
                if (samplesBuilder_ == null) {
                  ensureSamplesIsMutable();
                  samples_.add(m);
                } else {
                  samplesBuilder_.addMessage(m);
                }
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getCalcFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object dataId_ = "";
      /**
       * <code>string data_id = 1;</code>
       * @return The dataId.
       */
      public java.lang.String getDataId() {
        java.lang.Object ref = dataId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          dataId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string data_id = 1;</code>
       * @return The bytes for dataId.
       */
      public com.google.protobuf.ByteString
          getDataIdBytes() {
        java.lang.Object ref = dataId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          dataId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string data_id = 1;</code>
       * @param value The dataId to set.
       * @return This builder for chaining.
       */
      public Builder setDataId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        dataId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string data_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataId() {
        dataId_ = getDefaultInstance().getDataId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string data_id = 1;</code>
       * @param value The bytes for dataId to set.
       * @return This builder for chaining.
       */
      public Builder setDataIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        dataId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.util.List<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples> samples_ =
        java.util.Collections.emptyList();
      private void ensureSamplesIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          samples_ = new java.util.ArrayList<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples>(samples_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.SamplesOrBuilder> samplesBuilder_;

      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public java.util.List<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples> getSamplesList() {
        if (samplesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(samples_);
        } else {
          return samplesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public int getSamplesCount() {
        if (samplesBuilder_ == null) {
          return samples_.size();
        } else {
          return samplesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples getSamples(int index) {
        if (samplesBuilder_ == null) {
          return samples_.get(index);
        } else {
          return samplesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public Builder setSamples(
          int index, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples value) {
        if (samplesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSamplesIsMutable();
          samples_.set(index, value);
          onChanged();
        } else {
          samplesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public Builder setSamples(
          int index, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.Builder builderForValue) {
        if (samplesBuilder_ == null) {
          ensureSamplesIsMutable();
          samples_.set(index, builderForValue.build());
          onChanged();
        } else {
          samplesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public Builder addSamples(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples value) {
        if (samplesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSamplesIsMutable();
          samples_.add(value);
          onChanged();
        } else {
          samplesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public Builder addSamples(
          int index, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples value) {
        if (samplesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSamplesIsMutable();
          samples_.add(index, value);
          onChanged();
        } else {
          samplesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public Builder addSamples(
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.Builder builderForValue) {
        if (samplesBuilder_ == null) {
          ensureSamplesIsMutable();
          samples_.add(builderForValue.build());
          onChanged();
        } else {
          samplesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public Builder addSamples(
          int index, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.Builder builderForValue) {
        if (samplesBuilder_ == null) {
          ensureSamplesIsMutable();
          samples_.add(index, builderForValue.build());
          onChanged();
        } else {
          samplesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public Builder addAllSamples(
          java.lang.Iterable<? extends com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples> values) {
        if (samplesBuilder_ == null) {
          ensureSamplesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, samples_);
          onChanged();
        } else {
          samplesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public Builder clearSamples() {
        if (samplesBuilder_ == null) {
          samples_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          samplesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public Builder removeSamples(int index) {
        if (samplesBuilder_ == null) {
          ensureSamplesIsMutable();
          samples_.remove(index);
          onChanged();
        } else {
          samplesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.Builder getSamplesBuilder(
          int index) {
        return getSamplesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.SamplesOrBuilder getSamplesOrBuilder(
          int index) {
        if (samplesBuilder_ == null) {
          return samples_.get(index);  } else {
          return samplesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public java.util.List<? extends com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.SamplesOrBuilder> 
           getSamplesOrBuilderList() {
        if (samplesBuilder_ != null) {
          return samplesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(samples_);
        }
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.Builder addSamplesBuilder() {
        return getSamplesFieldBuilder().addBuilder(
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.getDefaultInstance());
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.Builder addSamplesBuilder(
          int index) {
        return getSamplesFieldBuilder().addBuilder(
            index, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.getDefaultInstance());
      }
      /**
       * <code>repeated .jlr.va.ResponseData.Data.Samples samples = 2;</code>
       */
      public java.util.List<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.Builder> 
           getSamplesBuilderList() {
        return getSamplesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.SamplesOrBuilder> 
          getSamplesFieldBuilder() {
        if (samplesBuilder_ == null) {
          samplesBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Samples.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.SamplesOrBuilder>(
                  samples_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          samples_ = null;
        }
        return samplesBuilder_;
      }

      private com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData calc_;
      private com.google.protobuf.SingleFieldBuilder<
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedDataOrBuilder> calcBuilder_;
      /**
       * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
       * @return Whether the calc field is set.
       */
      public boolean hasCalc() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
       * @return The calc.
       */
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData getCalc() {
        if (calcBuilder_ == null) {
          return calc_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.getDefaultInstance() : calc_;
        } else {
          return calcBuilder_.getMessage();
        }
      }
      /**
       * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
       */
      public Builder setCalc(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData value) {
        if (calcBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          calc_ = value;
        } else {
          calcBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
       */
      public Builder setCalc(
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.Builder builderForValue) {
        if (calcBuilder_ == null) {
          calc_ = builderForValue.build();
        } else {
          calcBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
       */
      public Builder mergeCalc(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData value) {
        if (calcBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            calc_ != null &&
            calc_ != com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.getDefaultInstance()) {
            getCalcBuilder().mergeFrom(value);
          } else {
            calc_ = value;
          }
        } else {
          calcBuilder_.mergeFrom(value);
        }
        if (calc_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
       */
      public Builder clearCalc() {
        bitField0_ = (bitField0_ & ~0x00000004);
        calc_ = null;
        if (calcBuilder_ != null) {
          calcBuilder_.dispose();
          calcBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
       */
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.Builder getCalcBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getCalcFieldBuilder().getBuilder();
      }
      /**
       * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
       */
      public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedDataOrBuilder getCalcOrBuilder() {
        if (calcBuilder_ != null) {
          return calcBuilder_.getMessageOrBuilder();
        } else {
          return calc_ == null ?
              com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.getDefaultInstance() : calc_;
        }
      }
      /**
       * <code>.jlr.va.ResponseData.Data.CalculatedData calc = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedDataOrBuilder> 
          getCalcFieldBuilder() {
        if (calcBuilder_ == null) {
          calcBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedData.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.CalculatedDataOrBuilder>(
                  getCalc(),
                  getParentForChildren(),
                  isClean());
          calc_ = null;
        }
        return calcBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:jlr.va.ResponseData.Data)
    }

    // @@protoc_insertion_point(class_scope:jlr.va.ResponseData.Data)
    private static final com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data();
    }

    public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Data>
        PARSER = new com.google.protobuf.AbstractParser<Data>() {
      @java.lang.Override
      public Data parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Data> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Data> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private int bitField0_;
  public static final int QUERY_ID_FIELD_NUMBER = 1;
  @SuppressWarnings("serial")
  private volatile java.lang.Object queryId_ = "";
  /**
   * <code>string query_id = 1;</code>
   * @return The queryId.
   */
  @java.lang.Override
  public java.lang.String getQueryId() {
    java.lang.Object ref = queryId_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      queryId_ = s;
      return s;
    }
  }
  /**
   * <code>string query_id = 1;</code>
   * @return The bytes for queryId.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getQueryIdBytes() {
    java.lang.Object ref = queryId_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      queryId_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int EVENT_TIMESTAMP_MS_FIELD_NUMBER = 2;
  private long eventTimestampMs_ = 0L;
  /**
   * <code>uint64 event_timestamp_ms = 2;</code>
   * @return The eventTimestampMs.
   */
  @java.lang.Override
  public long getEventTimestampMs() {
    return eventTimestampMs_;
  }

  public static final int DATA_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private java.util.List<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data> data_;
  /**
   * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
   */
  @java.lang.Override
  public java.util.List<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data> getDataList() {
    return data_;
  }
  /**
   * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.DataOrBuilder> 
      getDataOrBuilderList() {
    return data_;
  }
  /**
   * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
   */
  @java.lang.Override
  public int getDataCount() {
    return data_.size();
  }
  /**
   * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
   */
  @java.lang.Override
  public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data getData(int index) {
    return data_.get(index);
  }
  /**
   * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
   */
  @java.lang.Override
  public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.DataOrBuilder getDataOrBuilder(
      int index) {
    return data_.get(index);
  }

  public static final int GLOBALRT_FIELD_NUMBER = 4;
  private com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT globalRT_;
  /**
   * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
   * @return Whether the globalRT field is set.
   */
  @java.lang.Override
  public boolean hasGlobalRT() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
   * @return The globalRT.
   */
  @java.lang.Override
  public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT getGlobalRT() {
    return globalRT_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.getDefaultInstance() : globalRT_;
  }
  /**
   * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
   */
  @java.lang.Override
  public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRTOrBuilder getGlobalRTOrBuilder() {
    return globalRT_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.getDefaultInstance() : globalRT_;
  }

  public static final int EMIT_TIMESTAMP_MS_FIELD_NUMBER = 5;
  private long emitTimestampMs_ = 0L;
  /**
   * <code>uint64 emit_timestamp_ms = 5;</code>
   * @return The emitTimestampMs.
   */
  @java.lang.Override
  public long getEmitTimestampMs() {
    return emitTimestampMs_;
  }

  public static final int SESSION_POWERCYCLE_FIELD_NUMBER = 6;
  @SuppressWarnings("serial")
  private volatile java.lang.Object sessionPowercycle_ = "";
  /**
   * <code>string session_powercycle = 6;</code>
   * @return The sessionPowercycle.
   */
  @java.lang.Override
  public java.lang.String getSessionPowercycle() {
    java.lang.Object ref = sessionPowercycle_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      sessionPowercycle_ = s;
      return s;
    }
  }
  /**
   * <code>string session_powercycle = 6;</code>
   * @return The bytes for sessionPowercycle.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getSessionPowercycleBytes() {
    java.lang.Object ref = sessionPowercycle_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      sessionPowercycle_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(queryId_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 1, queryId_);
    }
    if (eventTimestampMs_ != 0L) {
      output.writeUInt64(2, eventTimestampMs_);
    }
    for (int i = 0; i < data_.size(); i++) {
      output.writeMessage(3, data_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(4, getGlobalRT());
    }
    if (emitTimestampMs_ != 0L) {
      output.writeUInt64(5, emitTimestampMs_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(sessionPowercycle_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 6, sessionPowercycle_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(queryId_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(1, queryId_);
    }
    if (eventTimestampMs_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(2, eventTimestampMs_);
    }
    for (int i = 0; i < data_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, data_.get(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getGlobalRT());
    }
    if (emitTimestampMs_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeUInt64Size(5, emitTimestampMs_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(sessionPowercycle_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(6, sessionPowercycle_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData)) {
      return super.equals(obj);
    }
    com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData other = (com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData) obj;

    if (!getQueryId()
        .equals(other.getQueryId())) return false;
    if (getEventTimestampMs()
        != other.getEventTimestampMs()) return false;
    if (!getDataList()
        .equals(other.getDataList())) return false;
    if (hasGlobalRT() != other.hasGlobalRT()) return false;
    if (hasGlobalRT()) {
      if (!getGlobalRT()
          .equals(other.getGlobalRT())) return false;
    }
    if (getEmitTimestampMs()
        != other.getEmitTimestampMs()) return false;
    if (!getSessionPowercycle()
        .equals(other.getSessionPowercycle())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + QUERY_ID_FIELD_NUMBER;
    hash = (53 * hash) + getQueryId().hashCode();
    hash = (37 * hash) + EVENT_TIMESTAMP_MS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getEventTimestampMs());
    if (getDataCount() > 0) {
      hash = (37 * hash) + DATA_FIELD_NUMBER;
      hash = (53 * hash) + getDataList().hashCode();
    }
    if (hasGlobalRT()) {
      hash = (37 * hash) + GLOBALRT_FIELD_NUMBER;
      hash = (53 * hash) + getGlobalRT().hashCode();
    }
    hash = (37 * hash) + EMIT_TIMESTAMP_MS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getEmitTimestampMs());
    hash = (37 * hash) + SESSION_POWERCYCLE_FIELD_NUMBER;
    hash = (53 * hash) + getSessionPowercycle().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code jlr.va.ResponseData}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:jlr.va.ResponseData)
      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseDataOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.class, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Builder.class);
    }

    // Construct using com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        getDataFieldBuilder();
        getGlobalRTFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      queryId_ = "";
      eventTimestampMs_ = 0L;
      if (dataBuilder_ == null) {
        data_ = java.util.Collections.emptyList();
      } else {
        data_ = null;
        dataBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000004);
      globalRT_ = null;
      if (globalRTBuilder_ != null) {
        globalRTBuilder_.dispose();
        globalRTBuilder_ = null;
      }
      emitTimestampMs_ = 0L;
      sessionPowercycle_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.RawProtobufMessage.internal_static_jlr_va_ResponseData_descriptor;
    }

    @java.lang.Override
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData getDefaultInstanceForType() {
      return com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.getDefaultInstance();
    }

    @java.lang.Override
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData build() {
      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData buildPartial() {
      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData result = new com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData result) {
      if (dataBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0)) {
          data_ = java.util.Collections.unmodifiableList(data_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.data_ = data_;
      } else {
        result.data_ = dataBuilder_.build();
      }
    }

    private void buildPartial0(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.queryId_ = queryId_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.eventTimestampMs_ = eventTimestampMs_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.globalRT_ = globalRTBuilder_ == null
            ? globalRT_
            : globalRTBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.emitTimestampMs_ = emitTimestampMs_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.sessionPowercycle_ = sessionPowercycle_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData) {
        return mergeFrom((com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData other) {
      if (other == com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.getDefaultInstance()) return this;
      if (!other.getQueryId().isEmpty()) {
        queryId_ = other.queryId_;
        bitField0_ |= 0x00000001;
        onChanged();
      }
      if (other.getEventTimestampMs() != 0L) {
        setEventTimestampMs(other.getEventTimestampMs());
      }
      if (dataBuilder_ == null) {
        if (!other.data_.isEmpty()) {
          if (data_.isEmpty()) {
            data_ = other.data_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureDataIsMutable();
            data_.addAll(other.data_);
          }
          onChanged();
        }
      } else {
        if (!other.data_.isEmpty()) {
          if (dataBuilder_.isEmpty()) {
            dataBuilder_.dispose();
            dataBuilder_ = null;
            data_ = other.data_;
            bitField0_ = (bitField0_ & ~0x00000004);
            dataBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 getDataFieldBuilder() : null;
          } else {
            dataBuilder_.addAllMessages(other.data_);
          }
        }
      }
      if (other.hasGlobalRT()) {
        mergeGlobalRT(other.getGlobalRT());
      }
      if (other.getEmitTimestampMs() != 0L) {
        setEmitTimestampMs(other.getEmitTimestampMs());
      }
      if (!other.getSessionPowercycle().isEmpty()) {
        sessionPowercycle_ = other.sessionPowercycle_;
        bitField0_ |= 0x00000020;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              queryId_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 16: {
              eventTimestampMs_ = input.readUInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data m =
                  input.readMessage(
                      com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.parser(),
                      extensionRegistry);
              if (dataBuilder_ == null) {
                ensureDataIsMutable();
                data_.add(m);
              } else {
                dataBuilder_.addMessage(m);
              }
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  getGlobalRTFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            case 40: {
              emitTimestampMs_ = input.readUInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              sessionPowercycle_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private java.lang.Object queryId_ = "";
    /**
     * <code>string query_id = 1;</code>
     * @return The queryId.
     */
    public java.lang.String getQueryId() {
      java.lang.Object ref = queryId_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        queryId_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string query_id = 1;</code>
     * @return The bytes for queryId.
     */
    public com.google.protobuf.ByteString
        getQueryIdBytes() {
      java.lang.Object ref = queryId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        queryId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string query_id = 1;</code>
     * @param value The queryId to set.
     * @return This builder for chaining.
     */
    public Builder setQueryId(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      queryId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>string query_id = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearQueryId() {
      queryId_ = getDefaultInstance().getQueryId();
      bitField0_ = (bitField0_ & ~0x00000001);
      onChanged();
      return this;
    }
    /**
     * <code>string query_id = 1;</code>
     * @param value The bytes for queryId to set.
     * @return This builder for chaining.
     */
    public Builder setQueryIdBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      queryId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }

    private long eventTimestampMs_ ;
    /**
     * <code>uint64 event_timestamp_ms = 2;</code>
     * @return The eventTimestampMs.
     */
    @java.lang.Override
    public long getEventTimestampMs() {
      return eventTimestampMs_;
    }
    /**
     * <code>uint64 event_timestamp_ms = 2;</code>
     * @param value The eventTimestampMs to set.
     * @return This builder for chaining.
     */
    public Builder setEventTimestampMs(long value) {

      eventTimestampMs_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>uint64 event_timestamp_ms = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearEventTimestampMs() {
      bitField0_ = (bitField0_ & ~0x00000002);
      eventTimestampMs_ = 0L;
      onChanged();
      return this;
    }

    private java.util.List<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data> data_ =
      java.util.Collections.emptyList();
    private void ensureDataIsMutable() {
      if (!((bitField0_ & 0x00000004) != 0)) {
        data_ = new java.util.ArrayList<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data>(data_);
        bitField0_ |= 0x00000004;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.DataOrBuilder> dataBuilder_;

    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public java.util.List<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data> getDataList() {
      if (dataBuilder_ == null) {
        return java.util.Collections.unmodifiableList(data_);
      } else {
        return dataBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public int getDataCount() {
      if (dataBuilder_ == null) {
        return data_.size();
      } else {
        return dataBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data getData(int index) {
      if (dataBuilder_ == null) {
        return data_.get(index);
      } else {
        return dataBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public Builder setData(
        int index, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataIsMutable();
        data_.set(index, value);
        onChanged();
      } else {
        dataBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public Builder setData(
        int index, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Builder builderForValue) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.set(index, builderForValue.build());
        onChanged();
      } else {
        dataBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public Builder addData(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataIsMutable();
        data_.add(value);
        onChanged();
      } else {
        dataBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public Builder addData(
        int index, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data value) {
      if (dataBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDataIsMutable();
        data_.add(index, value);
        onChanged();
      } else {
        dataBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public Builder addData(
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Builder builderForValue) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.add(builderForValue.build());
        onChanged();
      } else {
        dataBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public Builder addData(
        int index, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Builder builderForValue) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.add(index, builderForValue.build());
        onChanged();
      } else {
        dataBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public Builder addAllData(
        java.lang.Iterable<? extends com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data> values) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, data_);
        onChanged();
      } else {
        dataBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public Builder clearData() {
      if (dataBuilder_ == null) {
        data_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
      } else {
        dataBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public Builder removeData(int index) {
      if (dataBuilder_ == null) {
        ensureDataIsMutable();
        data_.remove(index);
        onChanged();
      } else {
        dataBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Builder getDataBuilder(
        int index) {
      return getDataFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.DataOrBuilder getDataOrBuilder(
        int index) {
      if (dataBuilder_ == null) {
        return data_.get(index);  } else {
        return dataBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public java.util.List<? extends com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.DataOrBuilder> 
         getDataOrBuilderList() {
      if (dataBuilder_ != null) {
        return dataBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(data_);
      }
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Builder addDataBuilder() {
      return getDataFieldBuilder().addBuilder(
          com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.getDefaultInstance());
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Builder addDataBuilder(
        int index) {
      return getDataFieldBuilder().addBuilder(
          index, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.getDefaultInstance());
    }
    /**
     * <code>repeated .jlr.va.ResponseData.Data data = 3;</code>
     */
    public java.util.List<com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Builder> 
         getDataBuilderList() {
      return getDataFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.DataOrBuilder> 
        getDataFieldBuilder() {
      if (dataBuilder_ == null) {
        dataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.Data.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.DataOrBuilder>(
                data_,
                ((bitField0_ & 0x00000004) != 0),
                getParentForChildren(),
                isClean());
        data_ = null;
      }
      return dataBuilder_;
    }

    private com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT globalRT_;
    private com.google.protobuf.SingleFieldBuilder<
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRTOrBuilder> globalRTBuilder_;
    /**
     * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
     * @return Whether the globalRT field is set.
     */
    public boolean hasGlobalRT() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
     * @return The globalRT.
     */
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT getGlobalRT() {
      if (globalRTBuilder_ == null) {
        return globalRT_ == null ? com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.getDefaultInstance() : globalRT_;
      } else {
        return globalRTBuilder_.getMessage();
      }
    }
    /**
     * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
     */
    public Builder setGlobalRT(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT value) {
      if (globalRTBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        globalRT_ = value;
      } else {
        globalRTBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
     */
    public Builder setGlobalRT(
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.Builder builderForValue) {
      if (globalRTBuilder_ == null) {
        globalRT_ = builderForValue.build();
      } else {
        globalRTBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
     */
    public Builder mergeGlobalRT(com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT value) {
      if (globalRTBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          globalRT_ != null &&
          globalRT_ != com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.getDefaultInstance()) {
          getGlobalRTBuilder().mergeFrom(value);
        } else {
          globalRT_ = value;
        }
      } else {
        globalRTBuilder_.mergeFrom(value);
      }
      if (globalRT_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
     */
    public Builder clearGlobalRT() {
      bitField0_ = (bitField0_ & ~0x00000008);
      globalRT_ = null;
      if (globalRTBuilder_ != null) {
        globalRTBuilder_.dispose();
        globalRTBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
     */
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.Builder getGlobalRTBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return getGlobalRTFieldBuilder().getBuilder();
    }
    /**
     * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
     */
    public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRTOrBuilder getGlobalRTOrBuilder() {
      if (globalRTBuilder_ != null) {
        return globalRTBuilder_.getMessageOrBuilder();
      } else {
        return globalRT_ == null ?
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.getDefaultInstance() : globalRT_;
      }
    }
    /**
     * <code>.jlr.va.ResponseData.GlobalRT globalRT = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRTOrBuilder> 
        getGlobalRTFieldBuilder() {
      if (globalRTBuilder_ == null) {
        globalRTBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRT.Builder, com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData.GlobalRTOrBuilder>(
                getGlobalRT(),
                getParentForChildren(),
                isClean());
        globalRT_ = null;
      }
      return globalRTBuilder_;
    }

    private long emitTimestampMs_ ;
    /**
     * <code>uint64 emit_timestamp_ms = 5;</code>
     * @return The emitTimestampMs.
     */
    @java.lang.Override
    public long getEmitTimestampMs() {
      return emitTimestampMs_;
    }
    /**
     * <code>uint64 emit_timestamp_ms = 5;</code>
     * @param value The emitTimestampMs to set.
     * @return This builder for chaining.
     */
    public Builder setEmitTimestampMs(long value) {

      emitTimestampMs_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <code>uint64 emit_timestamp_ms = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearEmitTimestampMs() {
      bitField0_ = (bitField0_ & ~0x00000010);
      emitTimestampMs_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object sessionPowercycle_ = "";
    /**
     * <code>string session_powercycle = 6;</code>
     * @return The sessionPowercycle.
     */
    public java.lang.String getSessionPowercycle() {
      java.lang.Object ref = sessionPowercycle_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        sessionPowercycle_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string session_powercycle = 6;</code>
     * @return The bytes for sessionPowercycle.
     */
    public com.google.protobuf.ByteString
        getSessionPowercycleBytes() {
      java.lang.Object ref = sessionPowercycle_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sessionPowercycle_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string session_powercycle = 6;</code>
     * @param value The sessionPowercycle to set.
     * @return This builder for chaining.
     */
    public Builder setSessionPowercycle(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      sessionPowercycle_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <code>string session_powercycle = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearSessionPowercycle() {
      sessionPowercycle_ = getDefaultInstance().getSessionPowercycle();
      bitField0_ = (bitField0_ & ~0x00000020);
      onChanged();
      return this;
    }
    /**
     * <code>string session_powercycle = 6;</code>
     * @param value The bytes for sessionPowercycle to set.
     * @return This builder for chaining.
     */
    public Builder setSessionPowercycleBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      sessionPowercycle_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:jlr.va.ResponseData)
  }

  // @@protoc_insertion_point(class_scope:jlr.va.ResponseData)
  private static final com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData();
  }

  public static com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<ResponseData>
      PARSER = new com.google.protobuf.AbstractParser<ResponseData>() {
    @java.lang.Override
    public ResponseData parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<ResponseData> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<ResponseData> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.jlr.vlc.protobuf.deserializer.protocol.raw.v3.ResponseData getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

