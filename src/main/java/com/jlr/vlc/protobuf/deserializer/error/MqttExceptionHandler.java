/*
 * Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
 */

package com.jlr.vlc.protobuf.deserializer.error;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@Slf4j
@ControllerAdvice
public class MqttExceptionHandler {

  @ExceptionHandler({MqttException.class})
  public void handleMqttException(MqttException e) {
    log.error(e.getMessage(), e.getCause());
  }

}
