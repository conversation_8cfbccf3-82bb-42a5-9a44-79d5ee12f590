FROM alpine:latest

LABEL maintainer="epopoola" version="0.1"

RUN apk upgrade --available
RUN apk add openjdk21 --no-cache

RUN mkdir /opt/application

RUN addgroup -g 2000 -S appgroup && adduser -u 1000 -S appuser -G appgroup
RUN wget -O /opt/application/dd-java-agent.jar https://dtdg.co/latest-java-tracer

WORKDIR /opt/application

COPY target/protobuf-deserializer*.jar app.jar

RUN chown -R 1000:2000 /opt/application

USER 1000

ENTRYPOINT ["java", "-javaagent:./dd-java-agent.jar", "-Djava.security.egd=file:/dev/./urandom","-jar", "app.jar"]


EXPOSE 8080
