include:
  - project: 'd9/infrastructure/gitlab-ci-templates'
    ref: master
    file: '.k8s-maven-gitlab-ci-template.yml'
  - project: 'D9/infrastructure/gitlab-ci-templates'
    ref: master
    file: '.k8s-maven-gitlab-ci-template-cn.patch.yml'
    inputs:
      deploy_aws_dev_cn: true
      deploy_aws_preprod_cn: true
      deploy_aws_prod_cn: true

variables:
  BASE_JAVA_JOB_IMAGE: maven:3.9.6-amazoncorretto-21

tests:
  tags:
    - aws-ec2-shell-vcdp-developers
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  services:
    - name: docker:20-dind
      alias: docker
      command: [ "--tls=false" ]

