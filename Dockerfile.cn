# There are issues with CN network connecting dockerHub and data-dog official site.
# Network issue is resolved by creating a new dockerfile and referring to a new mirror image registry.
# Data-dog issue is resolved by copying the jar package from a new image named dd-trace-java..


ARG REGISTRY
ARG BASE_JAVA_JOB_IMAGE

FROM ${REGISTRY}/datadog/dd-trace-java:latest as dd-trace-java

FROM ${REGISTRY}/${BASE_JAVA_JOB_IMAGE} as build
ARG CI_PROJECT_DIR
ARG MAVEN_OPTS
ARG MAVEN_CLI_OPTS
ARG MAVEN_SETTINGS_PATH

WORKDIR ${CI_PROJECT_DIR}

COPY ./ ./

RUN mvn ${MAVEN_CLI_OPTS} package -D skipTests --gs ${MAVEN_SETTINGS_PATH}

FROM ${REGISTRY}/alpine:latest
ARG CI_PROJECT_DIR

LABEL maintainer="dcooper" version="0.1"

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.ustc.edu.cn/g' /etc/apk/repositories
RUN apk upgrade --available
RUN apk add --no-cache openjdk21 curl

RUN mkdir /opt/application

RUN addgroup -g 2000 -S appgroup && adduser -u 1000 -S appuser -G appgroup
COPY --from=dd-trace-java /dd-java-agent.jar /opt/application/dd-java-agent.jar

WORKDIR /opt/application

COPY --from=build ${CI_PROJECT_DIR}/target/protobuf-deserializer*.jar app.jar

RUN chown -R 1000:2000 /opt/application

USER 1000

ENTRYPOINT ["java", "-javaagent:./dd-java-agent.jar", "-Djava.security.egd=file:/dev/./urandom","-jar", "app.jar"]

EXPOSE 8080
